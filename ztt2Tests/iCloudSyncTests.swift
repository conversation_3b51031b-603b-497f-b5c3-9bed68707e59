//
//  iCloudSyncTests.swift
//  ztt2Tests
//
//  Created by Augment Agent on 2025/8/3.
//

import XCTest
import CoreData
import CloudKit
@testable import ztt2

/**
 * iCloud同步功能测试
 * 测试新的动态切换功能和权限管理
 */
class iCloudSyncTests: XCTestCase {
    
    var persistenceController: PersistenceController!
    var syncManager: iCloudSyncManager!
    
    override func setUpWithError() throws {
        // 创建测试用的PersistenceController
        persistenceController = PersistenceController(inMemory: true)
        syncManager = iCloudSyncManager.shared
    }
    
    override func tearDownWithError() throws {
        persistenceController = nil
        syncManager = nil
    }
    
    // MARK: - 权限测试
    
    /**
     * 测试所有用户都有iCloud同步权限
     */
    func testAllUsersHavePermission() throws {
        // 测试：所有用户都应该有权限使用iCloud同步
        XCTAssertTrue(syncManager.hasPermission(), "所有用户都应该有权限使用iCloud同步")
    }
    
    // MARK: - PersistenceController测试
    
    /**
     * 测试PersistenceController初始化
     */
    func testPersistenceControllerInitialization() throws {
        // 测试内存模式初始化
        let inMemoryController = PersistenceController(inMemory: true)
        XCTAssertNotNil(inMemoryController.container, "内存模式容器应该正确初始化")
        
        // 测试正常模式初始化
        let normalController = PersistenceController(inMemory: false)
        XCTAssertNotNil(normalController.container, "正常模式容器应该正确初始化")
    }
    
    /**
     * 测试CloudKit可用性检查
     */
    func testCloudKitAvailabilityCheck() async throws {
        // 这个测试在模拟器中可能会失败，因为模拟器通常没有配置iCloud
        // 但我们可以测试方法是否正确执行
        let isAvailable = await persistenceController.checkCloudKitAvailability()
        
        // 在测试环境中，我们只验证方法能够执行而不抛出异常
        // 实际的可用性取决于测试环境的iCloud配置
        XCTAssertNotNil(isAvailable, "CloudKit可用性检查应该返回一个布尔值")
    }
    
    // MARK: - 同步管理器测试
    
    /**
     * 测试同步状态初始化
     */
    func testSyncManagerInitialization() throws {
        XCTAssertNotNil(syncManager, "同步管理器应该正确初始化")
        XCTAssertEqual(syncManager.syncStatus, .idle, "初始同步状态应该是idle")
    }
    
    /**
     * 测试同步设置存储
     */
    func testSyncSettingsStorage() throws {
        let ubiquitousStore = NSUbiquitousKeyValueStore.default
        
        // 测试设置存储
        ubiquitousStore.set(true, forKey: "icloud_sync_enabled")
        ubiquitousStore.synchronize()
        
        let storedValue = ubiquitousStore.bool(forKey: "icloud_sync_enabled")
        XCTAssertTrue(storedValue, "同步设置应该正确存储到NSUbiquitousKeyValueStore")
        
        // 清理测试数据
        ubiquitousStore.removeObject(forKey: "icloud_sync_enabled")
        ubiquitousStore.synchronize()
    }
    
    // MARK: - 数据操作测试
    
    /**
     * 测试基本数据操作
     */
    func testBasicDataOperations() throws {
        let context = persistenceController.container.viewContext
        
        // 创建测试用户
        let user = User(context: context)
        user.id = UUID()
        user.nickname = "测试用户"
        user.email = "<EMAIL>"
        user.createdAt = Date()
        
        // 保存数据
        persistenceController.save()
        
        // 验证数据保存成功
        let fetchRequest: NSFetchRequest<User> = User.fetchRequest()
        let users = try context.fetch(fetchRequest)
        
        XCTAssertEqual(users.count, 1, "应该有一个用户被保存")
        XCTAssertEqual(users.first?.nickname, "测试用户", "用户昵称应该正确保存")
    }
    
    // MARK: - 性能测试
    
    /**
     * 测试容器创建性能
     */
    func testContainerCreationPerformance() throws {
        measure {
            let controller = PersistenceController(inMemory: true)
            _ = controller.container
        }
    }
    
    /**
     * 测试数据保存性能
     */
    func testDataSavePerformance() throws {
        let context = persistenceController.container.viewContext
        
        measure {
            // 创建多个测试实体
            for i in 0..<100 {
                let user = User(context: context)
                user.id = UUID()
                user.nickname = "用户\(i)"
                user.email = "user\(i)@example.com"
                user.createdAt = Date()
            }
            
            persistenceController.save()
        }
    }
}

// MARK: - 模拟测试扩展

extension iCloudSyncTests {
    
    /**
     * 模拟iCloud同步启用流程
     */
    func testMockSyncEnableFlow() async throws {
        // 这是一个模拟测试，因为真实的CloudKit操作需要实际的iCloud环境
        
        // 1. 检查初始状态
        XCTAssertFalse(syncManager.isSyncEnabled, "初始状态应该是未启用同步")
        
        // 2. 模拟权限检查
        XCTAssertTrue(syncManager.hasPermission(), "应该有权限启用同步")
        
        // 3. 模拟可用性检查
        await syncManager.checkAvailability()
        // 注意：在测试环境中，isAvailable可能为false，这是正常的
        
        print("✅ 模拟同步启用流程测试完成")
    }
}
