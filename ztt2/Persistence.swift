//
//  Persistence.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import CoreData
import CloudKit

/**
 * 持久化控制器
 * 支持动态切换CloudKit和本地存储，无需重启应用
 */
class PersistenceController: ObservableObject {
    static let shared = PersistenceController()

    // MARK: - Published Properties
    @Published var isCloudKitEnabled: Bool = false
    @Published var isSwitching: Bool = false

    // MARK: - Private Properties
    private var _container: NSPersistentContainer?
    private let containerName = "ztt2"
    private let cloudKitContainerIdentifier = "iCloud.com.rainkygong.ztt2"

    // MARK: - Constants
    private let syncEnabledKey = "icloud_sync_enabled"

    // MARK: - Computed Properties

    /// 获取当前容器实例
    var container: NSPersistentContainer {
        if let existingContainer = _container {
            return existingContainer
        }

        // 首次初始化
        let newContainer = createContainer()
        _container = newContainer
        return newContainer
    }

    @MainActor
    static let preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        let viewContext = result.container.viewContext

        // 创建示例用户
        let user = User(context: viewContext)
        user.id = UUID()
        user.nickname = "示例用户"
        user.email = "<EMAIL>"
        user.createdAt = Date()

        // 创建示例订阅
        let subscription = Subscription(context: viewContext)
        subscription.id = UUID()
        subscription.subscriptionType = "free"
        subscription.isActive = true
        subscription.createdAt = Date()
        subscription.updatedAt = Date()
        subscription.user = user

        // 创建示例家庭成员
        let member = Member(context: viewContext)
        member.id = UUID()
        member.name = "小明"
        member.role = "son"
        member.birthDate = Calendar.current.date(byAdding: .year, value: -8, to: Date())
        member.memberNumber = 1
        member.currentPoints = 100
        member.createdAt = Date()
        member.updatedAt = Date()
        member.user = user

        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        return result
    }()

    // MARK: - Initialization

    init(inMemory: Bool = false) {
        // 从NSUbiquitousKeyValueStore读取同步偏好
        self.isCloudKitEnabled = NSUbiquitousKeyValueStore.default.bool(forKey: syncEnabledKey)

        if inMemory {
            _container = createInMemoryContainer()
        } else {
            _container = createContainer()
        }
    }

    // MARK: - Container Creation

    /// 创建内存容器（用于预览和测试）
    private func createInMemoryContainer() -> NSPersistentContainer {
        let container = NSPersistentContainer(name: containerName)
        container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")

        container.loadPersistentStores { _, error in
            if let error = error {
                fatalError("Failed to load in-memory store: \(error)")
            }
        }

        container.viewContext.automaticallyMergesChangesFromParent = true
        return container
    }

    /// 创建生产环境容器
    private func createContainer() -> NSPersistentContainer {
        let container: NSPersistentContainer

        // 根据当前设置选择容器类型
        if isCloudKitEnabled {
            container = NSPersistentCloudKitContainer(name: containerName)
        } else {
            container = NSPersistentContainer(name: containerName)
        }

        // 配置存储描述
        configureStoreDescription(container: container)

        // 加载存储
        loadPersistentStores(container: container)

        // 配置视图上下文
        container.viewContext.automaticallyMergesChangesFromParent = true

        return container
    }

    // MARK: - Store Configuration

    /// 配置存储描述
    private func configureStoreDescription(container: NSPersistentContainer) {
        guard let storeDescription = container.persistentStoreDescriptions.first else {
            fatalError("Failed to retrieve a persistent store description.")
        }

        // 基础配置
        storeDescription.setOption(true as NSNumber, forKey: NSMigratePersistentStoresAutomaticallyOption)
        storeDescription.setOption(true as NSNumber, forKey: NSInferMappingModelAutomaticallyOption)

        if isCloudKitEnabled {
            // CloudKit配置
            storeDescription.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
            storeDescription.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)

            // 设置CloudKit容器选项
            let cloudKitOptions = NSPersistentCloudKitContainerOptions(containerIdentifier: cloudKitContainerIdentifier)
            storeDescription.cloudKitContainerOptions = cloudKitOptions
        } else {
            // 本地存储配置
            storeDescription.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
            storeDescription.setOption(false as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)

            // 禁用CloudKit
            storeDescription.cloudKitContainerOptions = nil
        }

        // 确保数据库目录存在
        if let storeURL = storeDescription.url {
            let storeDirectory = storeURL.deletingLastPathComponent()
            do {
                try FileManager.default.createDirectory(at: storeDirectory, withIntermediateDirectories: true, attributes: nil)
                print("📁 Core Data store directory created: \(storeDirectory.path)")
            } catch {
                print("❌ Failed to create store directory: \(error)")
            }
        }

        print("📍 Core Data store URL: \(storeDescription.url?.path ?? "Unknown")")
        print("☁️ CloudKit enabled: \(isCloudKitEnabled)")
    }

    /// 加载持久化存储
    private func loadPersistentStores(container: NSPersistentContainer) {
        container.loadPersistentStores { storeDescription, error in
            if let error = error as NSError? {
                print("❌ Core Data error: \(error), \(error.userInfo)")

                // 处理常见错误
                switch error.code {
                case 513: // NSCocoaErrorDomain Code=513 - 权限错误
                    print("🔒 检测到权限错误，可能需要重新安装应用")
                    return
                case 134030, 4: // 文件不存在错误
                    print("📂 数据库文件不存在，将创建新的数据库")
                    return
                default:
                    fatalError("Unresolved Core Data error: \(error), \(error.userInfo)")
                }
            } else {
                print("✅ Core Data store loaded successfully: \(storeDescription.url?.path ?? "Unknown")")
            }
        }
    }

    // MARK: - Dynamic Switching

    /// 切换到CloudKit存储模式
    func enableCloudKitSync() async -> Bool {
        guard !isCloudKitEnabled else {
            print("⚠️ CloudKit sync is already enabled")
            return true
        }

        await MainActor.run {
            isSwitching = true
        }

        do {
            // 1. 保存当前更改
            try await saveCurrentChanges()

            // 2. 更新设置
            await MainActor.run {
                isCloudKitEnabled = true
                NSUbiquitousKeyValueStore.default.set(true, forKey: syncEnabledKey)
                NSUbiquitousKeyValueStore.default.synchronize()
            }

            // 3. 重新创建容器
            let newContainer = createContainer()

            // 4. 迁移数据（如果需要）
            let migrationSuccess = await migrateDataToCloudKit(from: _container, to: newContainer)

            if migrationSuccess {
                await MainActor.run {
                    _container = newContainer
                    isSwitching = false
                }
                print("✅ Successfully switched to CloudKit sync")
                return true
            } else {
                // 回滚设置
                await MainActor.run {
                    isCloudKitEnabled = false
                    NSUbiquitousKeyValueStore.default.set(false, forKey: syncEnabledKey)
                    isSwitching = false
                }
                print("❌ Failed to switch to CloudKit sync")
                return false
            }
        } catch {
            await MainActor.run {
                isCloudKitEnabled = false
                NSUbiquitousKeyValueStore.default.set(false, forKey: syncEnabledKey)
                isSwitching = false
            }
            print("❌ Error switching to CloudKit: \(error)")
            return false
        }
    }

    /// 切换到本地存储模式
    func disableCloudKitSync(deleteCloudData: Bool = false) async -> Bool {
        guard isCloudKitEnabled else {
            print("⚠️ CloudKit sync is already disabled")
            return true
        }

        await MainActor.run {
            isSwitching = true
        }

        do {
            // 1. 保存当前更改
            try await saveCurrentChanges()

            // 2. 停止CloudKit相关的观察者和通知
            await stopCloudKitObservers()

            // 3. 删除CloudKit数据（如果用户选择）
            if deleteCloudData {
                await deleteCloudKitData()
            }

            // 4. 更新设置
            await MainActor.run {
                isCloudKitEnabled = false
                NSUbiquitousKeyValueStore.default.set(false, forKey: syncEnabledKey)
                NSUbiquitousKeyValueStore.default.synchronize()
            }

            // 5. 安全地重新创建容器
            let newContainer = createContainer()

            // 6. 等待一段时间确保CloudKit完全停止
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

            await MainActor.run {
                _container = newContainer
                isSwitching = false
            }

            print("✅ Successfully switched to local storage")
            return true
        } catch {
            await MainActor.run {
                isSwitching = false
            }
            print("❌ Error switching to local storage: \(error)")
            return false
        }
    }


    // MARK: - Helper Methods

    /// 停止CloudKit相关的观察者和通知
    private func stopCloudKitObservers() async {
        print("🛑 Stopping CloudKit observers...")

        await MainActor.run {
            // 移除CloudKit相关的通知观察者
            NotificationCenter.default.removeObserver(
                self,
                name: .NSPersistentStoreRemoteChange,
                object: nil
            )

            // 移除其他可能的CloudKit通知
            if #available(iOS 14.0, *) {
                NotificationCenter.default.removeObserver(
                    self,
                    name: NSPersistentCloudKitContainer.eventChangedNotification,
                    object: nil
                )
            }

            print("✅ CloudKit observers stopped")
        }
    }

    /// 保存当前更改
    private func saveCurrentChanges() async throws {
        guard let currentContainer = _container else { return }

        let context = currentContainer.viewContext
        if context.hasChanges {
            try await context.perform {
                try context.save()
            }
        }
    }

    /// 迁移数据到CloudKit
    private func migrateDataToCloudKit(from oldContainer: NSPersistentContainer?, to newContainer: NSPersistentContainer) async -> Bool {
        guard let oldContainer = oldContainer else { return true }

        // 这里可以实现更复杂的数据迁移逻辑
        // 对于大多数情况，CoreData会自动处理迁移
        print("🔄 Migrating data to CloudKit...")

        // 等待一段时间让CloudKit初始化
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒

        return true
    }

    /// 删除CloudKit数据
    private func deleteCloudKitData() async {
        do {
            let container = CKContainer(identifier: cloudKitContainerIdentifier)
            let database = container.privateCloudDatabase

            // 删除CoreData的CloudKit zone
            let zoneID = CKRecordZone.ID(zoneName: "com.apple.coredata.cloudkit.zone")

            try await database.deleteRecordZone(withID: zoneID)
            print("🗑️ CloudKit data deleted successfully")
        } catch {
            print("❌ Failed to delete CloudKit data: \(error)")
        }
    }
}

// MARK: - 数据操作扩展
extension PersistenceController {

    /// 保存上下文
    func save() {
        let context = container.viewContext

        if context.hasChanges {
            do {
                try context.save()
            } catch {
                let nsError = error as NSError
                print("❌ Core Data保存失败: \(nsError.localizedDescription)")

                // 检查是否是文件不存在的错误，尝试重新初始化
                if nsError.code == 134030 || nsError.code == 4 {
                    // 重新加载存储
                    loadPersistentStores(container: container)

                    // 重新尝试保存
                    DispatchQueue.main.async {
                        do {
                            try context.save()
                            print("✅ 重新保存成功")
                        } catch {
                            print("❌ 重新保存失败: \(error.localizedDescription)")
                        }
                    }
                }
            }
        }
    }

    /// 获取当前用户
    func getCurrentUser() -> User? {
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.fetchLimit = 1

        do {
            let users = try container.viewContext.fetch(request)
            return users.first
        } catch {
            print("❌ Failed to fetch user: \(error)")
            return nil
        }
    }

    // 注意：已移除createDefaultUserIfNeeded方法
    // 现在用户必须通过Apple ID登录才能创建账号，确保数据正确关联

    /// 检查CloudKit可用性
    func checkCloudKitAvailability() async -> Bool {
        do {
            let container = CKContainer(identifier: cloudKitContainerIdentifier)
            let status = try await container.accountStatus()

            switch status {
            case .available:
                print("✅ CloudKit account is available")
                return true
            case .noAccount:
                print("⚠️ No iCloud account configured")
                return false
            case .restricted:
                print("⚠️ iCloud account is restricted")
                return false
            case .couldNotDetermine:
                print("⚠️ Could not determine iCloud account status")
                return false
            case .temporarilyUnavailable:
                print("⚠️ iCloud account is temporarily unavailable")
                return false
            @unknown default:
                print("⚠️ Unknown iCloud account status")
                return false
            }
        } catch {
            print("❌ Error checking CloudKit availability: \(error)")
            return false
        }
    }
}
