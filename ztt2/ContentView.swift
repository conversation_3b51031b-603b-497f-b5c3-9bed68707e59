//
//  ContentView.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject var authManager: AuthenticationManager
    @State private var isLoading = true

    var body: some View {
        Group {
            if isLoading {
                // 启动加载界面
                VStack(spacing: 20) {
                    Image("logo")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 80, height: 80)
                        .clipShape(RoundedRectangle(cornerRadius: 16))

                    Text("转团团")
                        .font(.largeTitle)
                        .fontWeight(.bold)

                    Text("家庭积分管理助手")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    ProgressView()
                        .scaleEffect(1.2)
                        .padding(.top)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(.systemBackground))
            } else if authManager.isLoggedIn {
                // 已登录，显示主界面
                MainTabView()
                    .environment(\.managedObjectContext, viewContext)
            } else {
                // 未登录，显示登录页面
                LoginView(authManager: authManager)
            }
        }
        .onAppear {
            initializeApp()
        }
    }

    private func initializeApp() {
        // 等待AuthenticationManager完成登录状态检查
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(.easeInOut(duration: 0.5)) {
                isLoading = false
            }

            if authManager.isLoggedIn {
                print("应用初始化完成，用户已登录")
            } else {
                print("应用初始化完成，等待用户登录")
            }
        }
    }
}



#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
