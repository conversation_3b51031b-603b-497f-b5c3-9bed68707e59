//
//  LoginView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/3.
//

import SwiftUI
import AuthenticationServices

/**
 * 登录页面视图
 * 包含登录页面logo、Apple登录按钮
 */
struct LoginView: View {
    
    // MARK: - Environment
    @Environment(\.colorScheme) private var colorScheme
    
    // MARK: - Dependencies
    @ObservedObject var authManager: AuthenticationManager
    
    // MARK: - State
    @StateObject private var viewModel: LoginViewModel
    @State private var agreementAccepted: Bool = false
    @State private var showUserAgreement: Bool = false
    @State private var showPrivacyPolicy: Bool = false
    @State private var showChildrenPrivacyPolicy: Bool = false
    
    // MARK: - Initialization
    init(authManager: AuthenticationManager) {
        self.authManager = authManager
        self._viewModel = StateObject(wrappedValue: LoginViewModel(authManager: authManager))
    }
    
    // MARK: - Body
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                createBackgroundGradient()
                
                // 主要内容
                VStack(spacing: 0) {
                    Spacer()
                    
                    // Logo区域
                    createLogoSection()
                    
                    Spacer()
                    
                    // 登录按钮区域
                    createLoginSection()

                    // 用户协议区域
                    createAgreementSection()

                    Spacer()
                        .frame(height: geometry.safeAreaInsets.bottom + 40)
                }
                .padding(.horizontal, 32)
            }
        }
        .ignoresSafeArea()
        .sheet(isPresented: $showUserAgreement) {
            UserAgreementView()
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            PrivacyPolicyView()
        }
        .sheet(isPresented: $showChildrenPrivacyPolicy) {
            ChildrenPrivacyPolicyView()
        }
        .alert("登录失败", isPresented: $viewModel.showError) {
            Button("确定") {
                viewModel.showError = false
            }
        } message: {
            Text(viewModel.errorMessage)
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 创建背景渐变
     */
    private func createBackgroundGradient() -> some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(hex: "#fcfff4"),
                Color(hex: "#f0f8e8")
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    /**
     * 创建Logo区域
     */
    private func createLogoSection() -> some View {
        VStack(spacing: 24) {
            // 应用Logo
            Image("logo")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 120, height: 120)
                .clipShape(RoundedRectangle(cornerRadius: 24))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            
            // 应用名称
            Text("转团团")
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
        }
    }
    
    /**
     * 创建登录按钮区域
     */
    private func createLoginSection() -> some View {
        VStack(spacing: 24) {
            // Apple登录按钮
            SignInWithAppleButton(
                onRequest: { request in
                    viewModel.handleSignInWithAppleRequest(request)
                },
                onCompletion: { result in
                    viewModel.handleSignInWithAppleCompletion(result)
                }
            )
            .signInWithAppleButtonStyle(colorScheme == .dark ? .white : .black)
            .frame(height: 50)
            .cornerRadius(25)
            .overlay(
                RoundedRectangle(cornerRadius: 25)
                    .stroke(DesignSystem.Colors.primary.opacity(0.3), lineWidth: 1)
            )
            .disabled(viewModel.isLoading || !agreementAccepted)
            .opacity((viewModel.isLoading || !agreementAccepted) ? 0.6 : 1.0)
            
            // 加载指示器
            if viewModel.isLoading {
                HStack(spacing: 8) {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("登录中...")
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .padding(.top, 8)
            }
        }
    }

    /**
     * 创建用户协议区域
     */
    private func createAgreementSection() -> some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            // 协议勾选
            Button(action: {
                agreementAccepted.toggle()

                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }) {
                HStack(alignment: .top, spacing: 14) {
                    // 勾选框 - 固定在顶部
                    ZStack {
                        RoundedRectangle(cornerRadius: 5)
                            .stroke(DesignSystem.Colors.primary, lineWidth: 1.5)
                            .frame(width: 22, height: 22)
                            .background(
                                RoundedRectangle(cornerRadius: 5)
                                    .fill(Color.white.opacity(0.1))
                            )

                        if agreementAccepted {
                            RoundedRectangle(cornerRadius: 5)
                                .fill(DesignSystem.Colors.primary)
                                .frame(width: 22, height: 22)

                            Image(systemName: "checkmark")
                                .font(.system(size: 13, weight: .bold))
                                .foregroundColor(.white)
                        }
                    }
                    .padding(.top, 1) // 微调对齐
                    .animation(.easeInOut(duration: 0.2), value: agreementAccepted)

                    // 协议文本 - 允许多行显示
                    createAgreementText()
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding(.horizontal, 4)
            }
            .buttonStyle(PlainButtonStyle())
            .padding(.horizontal, DesignSystem.Spacing.sm)
        }
    }

    /**
     * 创建协议文本
     */
    private func createAgreementText() -> some View {
        VStack(alignment: .leading, spacing: 6) {
            // 第一行：前缀文本 + 用户协议
            HStack(spacing: 2) {
                Text("login.agreement.prefix".localized)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .lineLimit(nil)

                Button(action: {
                    showUserAgreement = true
                    // 添加触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }) {
                    Text("login.agreement.user_agreement".localized)
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.primary)
                        .underline(true, color: DesignSystem.Colors.primary)
                }

                Text("login.agreement.and".localized)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)

                Spacer(minLength: 0)
            }

            // 第二行：隐私政策 + 儿童隐私政策
            HStack(spacing: 2) {
                Button(action: {
                    showPrivacyPolicy = true
                    // 添加触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }) {
                    Text("login.agreement.privacy_policy".localized)
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.primary)
                        .underline(true, color: DesignSystem.Colors.primary)
                }

                Text("login.agreement.and".localized)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)

                Button(action: {
                    showChildrenPrivacyPolicy = true
                    // 添加触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }) {
                    Text("login.agreement.children_privacy_policy".localized)
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.primary)
                        .underline(true, color: DesignSystem.Colors.primary)
                        .lineLimit(nil)
                }

                Spacer(minLength: 0)
            }
        }
    }
}

/**
 * 登录页面视图模型
 */
class LoginViewModel: ObservableObject {
    
    @Published var showError: Bool = false
    @Published var errorMessage: String = ""
    @Published var isLoading: Bool = false
    
    // MARK: - Dependencies
    private let authManager: AuthenticationManager
    
    // MARK: - Initialization
    init(authManager: AuthenticationManager) {
        self.authManager = authManager
    }
    
    /**
     * 处理Apple登录请求
     */
    func handleSignInWithAppleRequest(_ request: ASAuthorizationAppleIDRequest) {
        request.requestedScopes = [.fullName, .email]
        isLoading = true
    }
    
    /**
     * 处理Apple登录完成回调
     */
    func handleSignInWithAppleCompletion(_ result: Result<ASAuthorization, Error>) {
        isLoading = false
        
        switch result {
        case .success(let authorization):
            handleSuccessfulAuthorization(authorization)
        case .failure(let error):
            handleAuthorizationError(error)
        }
    }
    
    /**
     * 处理登录成功
     */
    private func handleSuccessfulAuthorization(_ authorization: ASAuthorization) {
        guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential else {
            showErrorMessage("登录凭证无效")
            return
        }
        
        // 获取用户信息
        let userID = appleIDCredential.user
        let fullName = appleIDCredential.fullName
        let email = appleIDCredential.email
        
        print("Apple登录成功:")
        print("用户ID: \(userID)")
        print("姓名: \(fullName?.formatted() ?? "未提供")")
        print("邮箱: \(email ?? "未提供")")
        
        // 通过AuthenticationManager处理登录成功
        authManager.handleSuccessfulLogin(userID: userID, fullName: fullName, email: email)
    }
    
    /**
     * 处理登录错误
     */
    private func handleAuthorizationError(_ error: Error) {
        let authError = error as? ASAuthorizationError
        
        switch authError?.code {
        case .canceled:
            // 用户取消登录，不显示错误
            break
        case .failed:
            showErrorMessage("登录失败，请重试")
        case .invalidResponse:
            showErrorMessage("登录响应无效")
        case .notHandled:
            showErrorMessage("登录请求未处理")
        case .unknown:
            showErrorMessage("未知登录错误")
        default:
            showErrorMessage("登录失败: \(error.localizedDescription)")
        }
        
        // 通过AuthenticationManager处理登录失败
        authManager.handleLoginFailure(error)
    }
    
    /**
     * 显示错误消息
     */
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
}

#Preview {
    LoginView(authManager: AuthenticationManager.shared)
}
