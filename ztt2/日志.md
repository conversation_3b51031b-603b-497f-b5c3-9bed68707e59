📁 Core Data store directory created: /var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application Support
📍 Core Data store URL: /var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application Support/ztt2.sqlite
☁️ CloudKit enabled: true
BUG IN CLIENT OF CLOUDKIT: CloudKit push notifications require the 'remote-notification' background mode in your info plist.
CoreData: debug: CoreData+CloudKit: -[PFCloudKitOptionsValidator validateOptions:andStoreOptions:error:](36): Validating options: <NSCloudKitMirroringDelegateOptions: 0x301d7c1b0> containerIdentifier:iCloud.com.rainkygong.ztt2 databaseScope:Private ckAssetThresholdBytes:<null> operationMemoryThresholdBytes:<null> useEncryptedStorage:NO useDeviceToDeviceEncryption:NO automaticallyDownloadFileBackedFutures:NO automaticallyScheduleImportAndExportOperations:YES skipCloudKitSetup:NO preserveLegacyRecordMetadataBehavior:NO useDaemon:YES apsConnectionMachServiceName:<null> containerProvider:<PFCloudKitContainerProvider: 0x302d74380> storeMonitorProvider:<PFCloudKitStoreMonitorProvider: 0x302d74390> metricsClient:<PFCloudKitMetricsClient: 0x302d743e0> metadataPurger:<PFCloudKitMetadataPurger: 0x302d743f0> scheduler:<null> notificationListener:<null> containerOptions:<null> defaultOperationConfiguration:<null> progressProvider:<NSPersistentCloudKitContainer: 0x303a284c0> test_useLegacySavePolicy:YES archivingUtilities:<PFCloudKitArchivingUtilities: 0x302d74400> bypassSchedulerActivityForInitialImport:NO bypassDasdRateLimiting:NO
storeOptions: {
    NSInferMappingModelAutomaticallyOption = 1;
    NSMigratePersistentStoresAutomaticallyOption = 1;
    NSPersistentCloudKitContainerOptionsKey = "<NSPersistentCloudKitContainerOptions: 0x300c48a00>";
    NSPersistentHistoryTrackingKey = 1;
    NSPersistentStoreMirroringOptionsKey =     {
        NSPersistentStoreMirroringDelegateOptionKey = "<NSCloudKitMirroringDelegate: 0x301060000>";
    };
    NSPersistentStoreRemoteChangeNotificationOptionKey = 1;
}
✅ Core Data store loaded successfully: /var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate observeChangesForStore:inPersistentStoreCoordinator:](423): <NSCloudKitMirroringDelegate: 0x301060000>: Observing store: <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _setUpCloudKitIntegration:](584): <NSCloudKitMirroringDelegate: 0x301060000>: Successfully enqueued setup request: <NSCloudKitMirroringDelegateSetupRequest: 0x300c58000> 494440F9-DE60-421F-AABD-2A2D43F6271D
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3548): <NSCloudKitMirroringDelegate: 0x301060000>: Executing: <NSCloudKitMirroringDelegateSetupRequest: 0x300c58000> 494440F9-DE60-421F-AABD-2A2D43F6271D
🔐 API密钥已存在于Keychain中
Info.plist configuration "(no name)" for UIWindowSceneSessionRoleApplication contained UISceneDelegateClassName key, but could not load class with name "ztt2.SceneDelegate".
Info.plist configuration "(no name)" for UIWindowSceneSessionRoleApplication contained UISceneDelegateClassName key, but could not load class with name "ztt2.SceneDelegate".
🔍 开始检查登录状态
💾 Keychain中的登录状态: true
💾 Keychain中的Apple用户ID: 001426.59411a4866d346eb8f0b45fe544652d9.1600
✅ 检测到Apple用户ID，开始验证Apple登录状态
Authorization change notification received for all containers
💾 用户信息保存成功
⚠️ 没有找到已登录用户，已清空数据
✅ 用户数据迁移完成: 家长
🔄 强制刷新用户状态: 家长, 订阅类型: free
应用初始化完成，用户已登录
🔄 开始初始化刮刮卡配置数据...
📝 没有选中成员，使用默认值
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _performSetupRequest:]_block_invoke(1138): <NSCloudKitMirroringDelegate: 0x301060000>: Successfully set up CloudKit integration for store (4F40BB33-799D-403B-8C2B-16613187D9A1): <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _beginWatchingForChanges:](768): <NSCloudKitMirroringDelegate: 0x301060000>: Scheduling post-setup export
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _finishedRequest:withResult:](3566): Finished request: <NSCloudKitMirroringDelegateSetupRequest: 0x300c58000> 494440F9-DE60-421F-AABD-2A2D43F6271D with result: <NSCloudKitMirroringResult: 0x3021ffed0> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x3014788f0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 1;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:](3659): <NSCloudKitMirroringDelegate: 0x301060000> - Beginning automated export - SandboxExport:
(null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate executeMirroringRequest:error:](976): <NSCloudKitMirroringDelegate: 0x301060000>: Asked to execute request: <NSCloudKitMirroringExportRequest: 0x300c4a800> CE352D4A-BF70-477F-A7E2-8DD73C07FED5
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedImportWithLabel:activity:voucher:completionHandler:](3620): <NSCloudKitMirroringDelegate: 0x301060000> - Beginning automated import - SandboxImport - in response to activity:
(null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate executeMirroringRequest:error:](976): <NSCloudKitMirroringDelegate: 0x301060000>: Asked to execute request: <NSCloudKitMirroringImportRequest: 0x300c4b520> 48E34981-914B-4BA0-9294-D661512E8C64
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3551): <NSCloudKitMirroringDelegate: 0x301060000>: No more requests to execute.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke(1010): <NSCloudKitMirroringDelegate: 0x301060000>: enqueuing request: <NSCloudKitMirroringExportRequest: 0x300c4a800> CE352D4A-BF70-477F-A7E2-8DD73C07FED5
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke_2(1019): Enqueued request: <NSCloudKitMirroringExportRequest: 0x300c4a800> CE352D4A-BF70-477F-A7E2-8DD73C07FED5
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke(1010): <NSCloudKitMirroringDelegate: 0x301060000>: enqueuing request: <NSCloudKitMirroringImportRequest: 0x300c4b520> 48E34981-914B-4BA0-9294-D661512E8C64
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke_2(1019): Enqueued request: <NSCloudKitMirroringImportRequest: 0x300c4b520> 48E34981-914B-4BA0-9294-D661512E8C64
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3548): <NSCloudKitMirroringDelegate: 0x301060000>: Executing: <NSCloudKitMirroringImportRequest: 0x300c4b520> 48E34981-914B-4BA0-9294-D661512E8C64
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3544): <NSCloudKitMirroringDelegate: 0x301060000>: Deferring additional work. There is still an active request: <NSCloudKitMirroringImportRequest: 0x300c4b520> 48E34981-914B-4BA0-9294-D661512E8C64
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464340>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 1;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:](3659): <NSCloudKitMirroringDelegate: 0x301060000> - Beginning automated export - ExportActivity:
<CKSchedulerActivity: 0x300b79d40; identifier=com.apple.coredata.cloudkit.activity.export.4F40BB33-799D-403B-8C2B-16613187D9A1, priority=2, container=iCloud.com.rainkygong.ztt2:Sandbox, relatedApplications=(
    "com.rainkygong.ztt2"
), xpcActivityCriteriaOverrides={
    ActivityGroupName = "com.apple.coredata.cloudkit.ztt2.4F40BB33-799D-403B-8C2B-16613187D9A1";
    Delay = 0;
    Priority = Utility;
}>
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate executeMirroringRequest:error:](976): <NSCloudKitMirroringDelegate: 0x301060000>: Asked to execute request: <NSCloudKitMirroringExportRequest: 0x300c57e30> 45E15FD3-3153-4337-9EE4-94142157984C
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke(1010): <NSCloudKitMirroringDelegate: 0x301060000>: enqueuing request: <NSCloudKitMirroringExportRequest: 0x300c57e30> 45E15FD3-3153-4337-9EE4-94142157984C
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke_2(1022): Failed to enqueue request: <NSCloudKitMirroringExportRequest: 0x300c57e30> 45E15FD3-3153-4337-9EE4-94142157984C
Error Domain=NSCocoaErrorDomain Code=134417 "Request '<NSCloudKitMirroringExportRequest: 0x300c57e30> 45E15FD3-3153-4337-9EE4-94142157984C' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'." UserInfo={NSLocalizedFailureReason=Request '<NSCloudKitMirroringExportRequest: 0x300c57e30> 45E15FD3-3153-4337-9EE4-94142157984C' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'.}
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:]_block_invoke(3673): <NSCloudKitMirroringDelegate: 0x301060000> - Finished automatic export - ExportActivity - with result: <NSCloudKitMirroringResult: 0x3021c6730> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 0 madeChanges: 0 error: Error Domain=NSCocoaErrorDomain Code=134417 "Request '<NSCloudKitMirroringExportRequest: 0x300c57e30> 45E15FD3-3153-4337-9EE4-94142157984C' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'." UserInfo={NSLocalizedFailureReason=Request '<NSCloudKitMirroringExportRequest: 0x300c57e30> 45E15FD3-3153-4337-9EE4-94142157984C' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'.}
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate finishedAutomatedRequestWithResult:](3579): Finished request '<NSCloudKitMirroringExportRequest: 0x300c57e30> 45E15FD3-3153-4337-9EE4-94142157984C' with result: <NSCloudKitMirroringResult: 0x3021c6730> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 0 madeChanges: 0 error: Error Domain=NSCocoaErrorDomain Code=134417 "Request '<NSCloudKitMirroringExportRequest: 0x300c57e30> 45E15FD3-3153-4337-9EE4-94142157984C' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'." UserInfo={NSLocalizedFailureReason=Request '<NSCloudKitMirroringExportRequest: 0x300c57e30> 45E15FD3-3153-4337-9EE4-94142157984C' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'.}
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitImporter databaseFetchFinishWithContext:error:completion:]_block_invoke(304): <PFCloudKitImporter: 0x303a27540>: Import request finished: <NSCloudKitMirroringImportRequest: 0x300c4b520> 48E34981-914B-4BA0-9294-D661512E8C64 - <PFCloudKitImportDatabaseContext: 0x3021c6520> {
Token: <CKServerChangeToken: 0x302d76330; data=AQAAAZhwsFWR>
Changed:
{(
    <CKRecordZoneID: 0x3021f0ae0; zoneName=com.apple.coredata.cloudkit.zone, ownerName=__defaultOwner__>
)}
}
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitImporter processWorkItemsWithCompletion:](418): <PFCloudKitImporter: 0x303a27540>: Processing work items: (
    "<PFCloudKitImporterZoneChangedWorkItem: 0x301878f20 - <NSCloudKitMirroringImportRequest: 0x300c4b520> 48E34981-914B-4BA0-9294-D661512E8C64> {\n(\n    \"<CKRecordZoneID: 0x3021f0ae0; zoneName=com.apple.coredata.cloudkit.zone, ownerName=__defaultOwner__>\"\n)\n}"
)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x3014788f0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 1;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:](3659): <NSCloudKitMirroringDelegate: 0x301060000> - Beginning automated export - ExportActivity:
<CKSchedulerActivity: 0x300b42460; identifier=com.apple.coredata.cloudkit.activity.export.4F40BB33-799D-403B-8C2B-16613187D9A1, priority=2, container=iCloud.com.rainkygong.ztt2:Sandbox, relatedApplications=(
    "com.rainkygong.ztt2"
), xpcActivityCriteriaOverrides={
    ActivityGroupName = "com.apple.coredata.cloudkit.ztt2.4F40BB33-799D-403B-8C2B-16613187D9A1";
    Delay = 0;
    Priority = Utility;
}>
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate executeMirroringRequest:error:](976): <NSCloudKitMirroringDelegate: 0x301060000>: Asked to execute request: <NSCloudKitMirroringExportRequest: 0x300c472f0> 6E601539-F1C1-4B52-9A34-4F7A095565B5
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke(1010): <NSCloudKitMirroringDelegate: 0x301060000>: enqueuing request: <NSCloudKitMirroringExportRequest: 0x300c472f0> 6E601539-F1C1-4B52-9A34-4F7A095565B5
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke_2(1022): Failed to enqueue request: <NSCloudKitMirroringExportRequest: 0x300c472f0> 6E601539-F1C1-4B52-9A34-4F7A095565B5
Error Domain=NSCocoaErrorDomain Code=134417 "Request '<NSCloudKitMirroringExportRequest: 0x300c472f0> 6E601539-F1C1-4B52-9A34-4F7A095565B5' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'." UserInfo={NSLocalizedFailureReason=Request '<NSCloudKitMirroringExportRequest: 0x300c472f0> 6E601539-F1C1-4B52-9A34-4F7A095565B5' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'.}
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:]_block_invoke(3673): <NSCloudKitMirroringDelegate: 0x301060000> - Finished automatic export - ExportActivity - with result: <NSCloudKitMirroringResult: 0x30210f870> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 0 madeChanges: 0 error: Error Domain=NSCocoaErrorDomain Code=134417 "Request '<NSCloudKitMirroringExportRequest: 0x300c472f0> 6E601539-F1C1-4B52-9A34-4F7A095565B5' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'." UserInfo={NSLocalizedFailureReason=Request '<NSCloudKitMirroringExportRequest: 0x300c472f0> 6E601539-F1C1-4B52-9A34-4F7A095565B5' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'.}
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate finishedAutomatedRequestWithResult:](3579): Finished request '<NSCloudKitMirroringExportRequest: 0x300c472f0> 6E601539-F1C1-4B52-9A34-4F7A095565B5' with result: <NSCloudKitMirroringResult: 0x30210f870> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 0 madeChanges: 0 error: Error Domain=NSCocoaErrorDomain Code=134417 "Request '<NSCloudKitMirroringExportRequest: 0x300c472f0> 6E601539-F1C1-4B52-9A34-4F7A095565B5' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'." UserInfo={NSLocalizedFailureReason=Request '<NSCloudKitMirroringExportRequest: 0x300c472f0> 6E601539-F1C1-4B52-9A34-4F7A095565B5' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'.}
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportRecordsWorkItem applyAccumulatedChangesToStore:inManagedObjectContext:withStoreMonitor:madeChanges:error:]_block_invoke(377): <PFCloudKitImporterZoneChangedWorkItem: 0x301878f20 - <NSCloudKitMirroringImportRequest: 0x300c4b520> 48E34981-914B-4BA0-9294-D661512E8C64> {
(
    "<CKRecordZoneID: 0x3021f0ae0; zoneName=com.apple.coredata.cloudkit.zone, ownerName=__defaultOwner__>"
)
} - Importing updated records:
(
    "<CKRecord: 0x10f209820; recordType=CD_User, recordID=7C3C0B57-538C-42CF-B32B-E6D4A41874AD:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=25, values={\n    \"CD_appleUserID\" = \"001426.59411a4866d346eb8f0b45fe544652d9.1600\";\n    \"CD_createdAt\" = \"2025-08-03 14:43:29 +0000\";\n    \"CD_entityName\" = User;\n    \"CD_id\" = \"F4481D57-B428-4F0D-86D9-377FD1EDA501\";\n    \"CD_nickname\" = \"\\U5bb6\\U957f\";\n    \"CD_subscription\" = \"982F49A1-B158-4C09-AF07-4C64CFC6AD39\";\n}>",
    "<CKRecord: 0x10f20a3a0; recordType=CD_Subscription, recordID=982F49A1-B158-4C09-AF07-4C64CFC6AD39:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=24, values={\n    \"CD_createdAt\" = \"2025-08-03 14:43:29 +0000\";\n    \"CD_endDate\" = \"2026-08-03 14:43:44 +0000\";\n    \"CD_entityName\" = Subscription;\n    \"CD_id\" = \"094E196F-5A75-47B9-A706-D32FDB5012C2\";\n    \"CD_isActive\" = 1;\n    \"CD_productIdentifier\" = \"com.ztt.premium.yearly\";\n    \"CD_startDate\" = \"2025-08-03 14:43:55 +0000\";\n    \"CD_subscriptionType\" = premium;\n    \"CD_updatedAt\" = \"2025-08-03 14:43:55 +0000\";\n    \"CD_user\" = \"7C3C0B57-538C-42CF-B32B-E6D4A41874AD\";\n}>",
    "<CKRecord: 0x10f20a710; recordType=CD_Member, recordID=10AC02AB-D278-4432-9DCE-A17346D2EE06:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=23, values={\n    \"CD_createdAt\" = \"2025-08-03 14:43:55 +0000\";\n    \"CD_currentPoints\" = 0;\n    \"CD_entityName\" = Member;\n    \"CD_id\" = \"CF8C3BFE-4C0D-4448-AF9D-AD35AD040206\";\n    \"CD_memberNumber\" = 1;\n    \"CD_name\" = \"\\U793a\\U4f8b\\U6210\\U5458\";\n    \"CD_role\" = son;\n    \"CD_updatedAt\" = \"2025-08-03 14:43:55 +0000\";\n    \"CD_user\" = \"7C3C0B57-538C-42CF-B32B-E6D4A41874AD\";\n}>",
    "<CKRecord: 0x10f20a860; recordType=CD_Member, recordID=87B69B0E-BCDA-4AA8-85BA-FB3113683C61:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=22, values={\n    \"CD_birthDate\" = \"2025-08-03 14:44:51 +0000\";\n    \"CD_createdAt\" = \"2025-08-03 14:44:59 +0000\";\n    \"CD_currentPoints\" = 0;\n    \"CD_entityName\" = Member;\n    \"CD_id\" = \"96C0AB6F-3497-4F41-BA7C-8821B5545DB1\";\n    \"CD_memberNumber\" = 2;\n    \"CD_name\" = \"\\U6211\\U662f\";\n    \"CD_role\" = son;\n    \"CD_updatedAt\" = \"2025-08-03 14:44:59 +0000\";\n    \"CD_user\" = \"7C3C0B57-538C-42CF-B32B-E6D4A41874AD\";\n}>",
    "<CKRecord: 0x10f20a180; recordType=CD_GlobalRule, recordID=3A3137E8-F5A1-4846-9698-7EB9A4D422F2:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=21, values={\n    \"CD_createdAt\" = \"2025-08-03 14:43:55 +0000\";\n    \"CD_entityName\" = GlobalRule;\n    \"CD_id\" = \"06733D4E-150D-4100-AA91-2F59B393BEE8\";\n    \"CD_isFrequent\" = 1;\n    \"CD_name\" = \"\\U5b8c\\U6210\\U4f5c\\U4e1a\";\n    \"CD_type\" = add;\n    \"CD_user\" = \"7C3C0B57-538C-42CF-B32B-E6D4A41874AD\";\n    \"CD_value\" = 10;\n}>",
    "<CKRecord: 0x10f20a9b0; recordType=CD_Member, recordID=A85FFA0E-B82B-44D6-987C-16D8CD70361E:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=20, values={\n    \"CD_birthDate\" = \"2025-08-03 16:03:37 +0000\";\n    \"CD_createdAt\" = \"2025-08-03 16:03:50 +0000\";\n    \"CD_currentPoints\" = 12;\n    \"CD_entityName\" = Member;\n    \"CD_id\" = \"05AE2575-89E5-4170-931D-3FD65BDD168F\";\n    \"CD_memberNumber\" = 1;\n    \"CD_name\" = \"\\U56fe\\U56fe\";\n    \"CD_role\" = daughter;\n    \"CD_updatedAt\" = \"2025-08-03 16:03:50 +0000\";\n    \"CD_user\" = \"2FC2E655-9FDE-4139-AA6D-D053B89CD6B2\";\n}>",
    "<CKRecord: 0x10f209b90; recordType=CD_User, recordID=2FC2E655-9FDE-4139-AA6D-D053B89CD6B2:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1z, values={\n    \"CD_appleUserID\" = \"001426.59411a4866d346eb8f0b45fe544652d9.1600\";\n    \"CD_createdAt\" = \"2025-08-03 15:56:29 +0000\";\n    \"CD_entityName\" = User;\n    \"CD_id\" = \"C52C3508-091D-4848-B7E7-9A4D192060C8\";\n    \"CD_nickname\" = \"\\U5bb6\\U957f\";\n    \"CD_subscription\" = \"DDAF8670-5B1D-41C2-B9B7-F7B508A54017\";\n}>",
    "<CKRecord: 0x10f20ab00; recordType=CD_Subscription, recordID=DDAF8670-5B1D-41C2-B9B7-F7B508A54017:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1x, values={\n    \"CD_createdAt\" = \"2025-08-03 15:56:29 +0000\";\n    \"CD_entityName\" = Subscription;\n    \"CD_id\" = \"87F5E4BB-C9C4-43C5-97B9-04DA72EB6E27\";\n    \"CD_isActive\" = 1;\n    \"CD_subscriptionType\" = free;\n    \"CD_updatedAt\" = \"2025-08-03 15:56:29 +0000\";\n    \"CD_user\" = \"2FC2E655-9FDE-4139-AA6D-D053B89CD6B2\";\n}>",
    "<CKRecord: 0x10f20a4f0; recordType=CD_User, recordID=6331FC0B-22EF-413A-91F3-2F53392B8227:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1w, values={\n    \"CD_appleUserID\" = \"001426.59411a4866d346eb8f0b45fe544652d9.1600\";\n    \"CD_createdAt\" = \"2025-08-03 14:39:16 +0000\";\n    \"CD_entityName\" = User;\n    \"CD_id\" = \"C9BCACAF-DBA2-4C4D-B3DC-C50A58A1BD87\";\n    \"CD_nickname\" = \"\\U5bb6\\U957f\";\n    \"CD_subscription\" = \"6F774032-9AE8-4723-9857-DF96D823E9BE\";\n}>",
    "<CKRecord: 0x10f20ae70; recordType=CD_Subscription, recordID=6F774032-9AE8-4723-9857-DF96D823E9BE:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1v, values={\n    \"CD_createdAt\" = \"2025-08-03 14:39:16 +0000\";\n    \"CD_endDate\" = \"2026-08-03 14:39:35 +0000\";\n    \"CD_entityName\" = Subscription;\n    \"CD_id\" = \"3A6745D5-6F9A-4D8B-85C2-6B595A7068AA\";\n    \"CD_isActive\" = 1;\n    \"CD_productIdentifier\" = \"com.ztt.premium.yearly\";\n    \"CD_startDate\" = \"2025-08-03 14:39:41 +0000\";\n    \"CD_subscriptionType\" = premium;\n    \"CD_updatedAt\" = \"2025-08-03 14:39:41 +0000\";\n    \"CD_user\" = \"6331FC0B-22EF-413A-91F3-2F53392B8227\";\n}>",
    "<CKRecord: 0x10f107540; recordType=CD_Member, recordID=17E832A8-038A-4DB0-BD6D-494F828C9D70:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1u, values={\n    \"CD_createdAt\" = \"2025-08-03 14:39:41 +0000\";\n    \"CD_currentPoints\" = 0;\n    \"CD_entityName\" = Member;\n    \"CD_id\" = \"DD14E826-9D4F-4704-8CE3-0F1BD051AE4A\";\n    \"CD_memberNumber\" = 1;\n    \"CD_name\" = \"\\U793a\\U4f8b\\U6210\\U5458\";\n    \"CD_role\" = son;\n    \"CD_updatedAt\" = \"2025-08-03 14:39:41 +0000\";\n    \"CD_user\" = \"6331FC0B-22EF-413A-91F3-2F53392B8227\";\n}>",
    "<CKRecord: 0x10f108640; recordType=CD_GlobalRule, recordID=EBB0DBEB-BE11-448A-9885-57B56EF716E4:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1t, values={\n    \"CD_createdAt\" = \"2025-08-03 14:39:41 +0000\";\n    \"CD_entityName\" = GlobalRule;\n    \"CD_id\" = \"3243EBF9-50AD-4828-93AF-A9A4162E5D76\";\n    \"CD_isFrequent\" = 1;\n    \"CD_name\" = \"\\U5b8c\\U6210\\U4f5c\\U4e1a\";\n    \"CD_type\" = add;\n    \"CD_user\" = \"6331FC0B-22EF-413A-91F3-2F53392B8227\";\n    \"CD_value\" = 10;\n}>",
    "<CKRecord: 0x10f107d40; recordType=CD_User, recordID=180FA254-01F9-4240-933E-1209393BA6FF:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1s, values={\n    \"CD_appleUserID\" = \"001426.59411a4866d346eb8f0b45fe544652d9.1600\";\n    \"CD_createdAt\" = \"2025-08-03 14:10:08 +0000\";\n    \"CD_entityName\" = User;\n    \"CD_id\" = \"F88CFDC0-FAE8-41E6-8F4D-FEA4396BBCD6\";\n    \"CD_nickname\" = \"\\U5bb6\\U957f\";\n    \"CD_subscription\" = \"8F17728C-2CFE-48DE-BF59-71DF01394A46\";\n}>",
    "<CKRecord: 0x10f105c10; recordType=CD_Subscription, recordID=8F17728C-2CFE-48DE-BF59-71DF01394A46:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1r, values={\n    \"CD_createdAt\" = \"2025-08-03 14:10:08 +0000\";\n    \"CD_endDate\" = \"2026-08-03 14:10:21 +0000\";\n    \"CD_entityName\" = Subscription;\n    \"CD_id\" = \"639B078F-A5BC-4DDB-9C89-96A6E15A002F\";\n    \"CD_isActive\" = 1;\n    \"CD_productIdentifier\" = \"com.ztt.basic.yearly\";\n    \"CD_startDate\" = \"2025-08-03 14:10:21 +0000\";\n    \"CD_subscriptionType\" = basic;\n    \"CD_updatedAt\" = \"2025-08-03 14:10:21 +0000\";\n    \"CD_user\" = \"180FA254-01F9-4240-933E-1209393BA6FF\";\n}>",
    "<CKRecord: 0x10f1080b0; recordType=CD_User, recordID=53393E2A-44DB-4E56-A7AD-2FFF62744155:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1q, values={\n    \"CD_appleUserID\" = \"001426.59411a4866d346eb8f0b45fe544652d9.1600\";\n    \"CD_createdAt\" = \"2025-08-03 14:07:38 +0000\";\n    \"CD_entityName\" = User;\n    \"CD_id\" = \"F6BE0B8B-C9C7-458A-9A9D-597C2458FB0D\";\n    \"CD_nickname\" = \"\\U5bb6\\U957f\";\n    \"CD_subscription\" = \"E7B54033-B5D7-4AFC-86EF-6A0468EF06FD\";\n}>",
    "<CKRecord: 0x10f108200; recordType=CD_Subscription, recordID=E7B54033-B5D7-4AFC-86EF-6A0468EF06FD:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1p, values={\n    \"CD_createdAt\" = \"2025-08-03 14:07:38 +0000\";\n    \"CD_endDate\" = \"2026-08-03 14:08:45 +0000\";\n    \"CD_entityName\" = Subscription;\n    \"CD_id\" = \"CEC450CC-2CF7-45DE-838F-0354BF941C8C\";\n    \"CD_isActive\" = 1;\n    \"CD_productIdentifier\" = \"com.ztt.premium.yearly\";\n    \"CD_startDate\" = \"2025-08-03 14:08:45 +0000\";\n    \"CD_subscriptionType\" = premium;\n    \"CD_updatedAt\" = \"2025-08-03 14:08:45 +0000\";\n    \"CD_user\" = \"53393E2A-44DB-4E56-A7AD-2FFF62744155\";\n}>",
    "<CKRecord: 0x10f10a500; recordType=CD_Member, recordID=C80999E7-E183-46C7-A853-8210632E685A:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1o, values={\n    \"CD_birthDate\" = \"2019-08-02 16:00:00 +0000\";\n    \"CD_createdAt\" = \"2025-08-03 14:08:39 +0000\";\n    \"CD_currentPoints\" = 100;\n    \"CD_entityName\" = Member;\n    \"CD_id\" = \"03E3703F-63CE-489C-8FBE-78363E8EFC00\";\n    \"CD_memberNumber\" = 1;\n    \"CD_name\" = \"\\U723d\";\n    \"CD_role\" = daughter;\n    \"CD_updatedAt\" = \"2025-08-03 14:08:39 +0000\";\n    \"CD_user\" = \"53393E2A-44DB-4E56-A7AD-2FFF62744155\";\n}>",
    "<CKRecord: 0x10f10a650; recordType=CD_Subscription, recordID=ADD27B7A-A968-4E2F-847F-615C41CADD4B:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1n, values={\n    \"CD_createdAt\" = \"2025-08-03 13:48:31 +0000\";\n    \"CD_entityName\" = Subscription;\n    \"CD_id\" = \"721EBEA3-66F5-4297-8DDD-8200462CEAC5\";\n    \"CD_isActive\" = 0;\n    \"CD_subscriptionType\" = free;\n    \"CD_updatedAt\" = \"2025-08-03 13:56:12 +0000\";\n    \"CD_user\" = \"F0754E26-EDD6-4A01-A0E4-12C5F1CDCF57\";\n}>",
    "<CKRecord: 0x10f105d60; recordType=CD_User, recordID=E6314B9D-D265-4819-8147-B748B3E88EB4:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1m, values={\n    \"CD_appleUserID\" = \"001426.59411a4866d346eb8f0b45fe544652d9.1600\";\n    \"CD_createdAt\" = \"2025-08-03 13:47:08 +0000\";\n    \"CD_entityName\" = User;\n    \"CD_id\" = \"A0793710-585C-4106-B03E-DC65E2F645AA\";\n    \"CD_nickname\" = \"\\U5bb6\\U957f\";\n    \"CD_subscription\" = \"2916D1BA-20A4-48E1-B74F-B77690FADB7F\";\n}>",
    "<CKRecord: 0x10f10a7a0; recordType=CD_Subscription, recordID=2916D1BA-20A4-48E1-B74F-B77690FADB7F:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1l, values={\n    \"CD_createdAt\" = \"2025-08-03 13:47:08 +0000\";\n    \"CD_endDate\" = \"2026-08-03 13:49:30 +0000\";\n    \"CD_entityName\" = Subscription;\n    \"CD_id\" = \"FD6CE076-5984-486B-A22E-2B6698DAAB76\";\n    \"CD_isActive\" = 1;\n    \"CD_productIdentifier\" = \"com.ztt.premium.yearly\";\n    \"CD_startDate\" = \"2025-08-03 13:49:30 +0000\";\n    \"CD_subscriptionType\" = premium;\n    \"CD_updatedAt\" = \"2025-08-03 13:49:30 +0000\";\n    \"CD_user\" = \"E6314B9D-D265-4819-8147-B748B3E88EB4\";\n}>",
    "<CKRecord: 0x10f10a8f0; recordType=CD_Member, recordID=07F38EFA-440C-434C-B9F3-BFC061054865:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1k, values={\n    \"CD_birthDate\" = \"2015-08-02 16:00:00 +0000\";\n    \"CD_createdAt\" = \"2025-08-03 13:49:16 +0000\";\n    \"CD_currentPoints\" = 88;\n    \"CD_entityName\" = Member;\n    \"CD_id\" = \"F02C09A8-8C4A-4C69-8C60-7B54E4F50AF9\";\n    \"CD_memberNumber\" = 1;\n    \"CD_name\" = \"\\U56fe\\U56fe\";\n    \"CD_role\" = daughter;\n    \"CD_updatedAt\" = \"2025-08-03 13:49:16 +0000\";\n    \"CD_user\" = \"E6314B9D-D265-4819-8147-B748B3E88EB4\";\n}>",
    "<CKRecord: 0x10f10aa40; recordType=CD_User, recordID=F0754E26-EDD6-4A01-A0E4-12C5F1CDCF57:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1j, values={\n    \"CD_appleUserID\" = \"001426.59411a4866d346eb8f0b45fe544652d9.1600\";\n    \"CD_createdAt\" = \"2025-08-03 13:48:31 +0000\";\n    \"CD_entityName\" = User;\n    \"CD_id\" = \"BDADB9CD-E164-48DD-BB0A-6FDEB4BB55E0\";\n    \"CD_nickname\" = \"\\U5bb6\\U957f\";\n    \"CD_subscription\" = \"ADD27B7A-A968-4E2F-847F-615C41CADD4B\";\n}>",
    "<CKRecord: 0x10f10ab90; recordType=CD_Member, recordID=3BFA499F-5D63-4B09-A197-09F03A26EE27:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1b, values={\n    \"CD_birthDate\" = \"2017-08-02 16:00:00 +0000\";\n    \"CD_createdAt\" = \"2025-08-03 12:36:52 +0000\";\n    \"CD_currentPoints\" = 210;\n    \"CD_entityName\" = Member;\n    \"CD_id\" = \"3EF68398-77F9-4895-984B-796D9520C304\";\n    \"CD_memberNumber\" = 1;\n    \"CD_name\" = \"\\U9093\\U723d\";\n    \"CD_role\" = daughter;\n    \"CD_updatedAt\" = \"2025-08-03 12:36:52 +0000\";\n    \"CD_user\" = \"61312E2B-2E12-496B-A31F-9024D36EF202\";\n}>",
    "<CKRecord: 0x10f10af00; recordType=CD_User, recordID=61312E2B-2E12-496B-A31F-9024D36EF202:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=1a, values={\n    \"CD_appleUserID\" = \"001426.59411a4866d346eb8f0b45fe544652d9.1600\";\n    \"CD_createdAt\" = \"2025-08-03 13:42:37 +0000\";\n    \"CD_email\" = \"<EMAIL>\";\n    \"CD_entityName\" = User;\n    \"CD_id\" = \"51249411-AC78-45A5-A20B-E48A06A61729\";\n    \"CD_nickname\" = \"\\U9f9a\\U7948\\U5b87\";\n    \"CD_subscription\" = \"E985AF71-C5FD-41E2-A15E-46EE3F71859E\";\n}>",
    "<CKRecord: 0x10f10ace0; recordType=CD_Member, recordID=1D4E85BD-6120-4DA8-86FA-CB60DAF22712:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=19, values={\n    \"CD_birthDate\" = \"2018-05-02 16:00:00 +0000\";\n    \"CD_createdAt\" = \"2025-08-03 08:27:19 +0000\";\n    \"CD_currentPoints\" = 99;\n    \"CD_entityName\" = Member;\n    \"CD_id\" = \"8BC69819-4DE1-4229-9CEF-BF296D8656AA\";\n    \"CD_memberNumber\" = 1;\n    \"CD_name\" = \"\\U9093\\U723d\";\n    \"CD_role\" = daughter;\n    \"CD_updatedAt\" = \"2025-08-03 08:31:59 +0000\";\n    \"CD_user\" = \"61312E2B-2E12-496B-A31F-9024D36EF202\";\n}>",
    "<CKRecord: 0x10f107e90; recordType=CD_Member, recordID=34700A9F-4B97-43B0-9F47-C76B69A2A22C:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=18, values={\n    \"CD_birthDate\" = \"2019-04-02 16:00:00 +0000\";\n    \"CD_createdAt\" = \"2025-08-03 08:32:32 +0000\";\n    \"CD_currentPoints\" = 100;\n    \"CD_entityName\" = Member;\n    \"CD_id\" = \"5AFA89BB-888D-43CA-9FEA-571EC103379F\";\n    \"CD_memberNumber\" = 2;\n    \"CD_name\" = \"\\U56fe\\U56fe\";\n    \"CD_role\" = daughter;\n    \"CD_updatedAt\" = \"2025-08-03 08:32:32 +0000\";\n    \"CD_user\" = \"61312E2B-2E12-496B-A31F-9024D36EF202\";\n}>",
    "<CKRecord: 0x10f43bd20; recordType=CD_Subscription, recordID=E985AF71-C5FD-41E2-A15E-46EE3F71859E:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=14, values={\n    \"CD_createdAt\" = \"2025-08-03 13:42:37 +0000\";\n    \"CD_endDate\" = \"2026-08-03 13:44:38 +0000\";\n    \"CD_entityName\" = Subscription;\n    \"CD_id\" = \"3C8FF748-F669-4AB7-AB9B-58CE6249AF6C\";\n    \"CD_isActive\" = 1;\n    \"CD_productIdentifier\" = \"com.ztt.premium.yearly\";\n    \"CD_startDate\" = \"2025-08-03 13:44:38 +0000\";\n    \"CD_subscriptionType\" = premium;\n    \"CD_updatedAt\" = \"2025-08-03 13:44:38 +0000\";\n    \"CD_user\" = \"61312E2B-2E12-496B-A31F-9024D36EF202\";\n}>",
    "<CKRecord: 0x10f43c190; recordType=CD_Member, recordID=82107A21-A6E2-4D21-95F9-5878BB7730A9:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=11, values={\n    \"CD_birthDate\" = \"2016-08-02 16:00:00 +0000\";\n    \"CD_createdAt\" = \"2025-08-03 13:43:48 +0000\";\n    \"CD_currentPoints\" = 100;\n    \"CD_entityName\" = Member;\n    \"CD_id\" = \"B00E4595-C087-4F87-9DB4-1086EA7429F4\";\n    \"CD_memberNumber\" = 1;\n    \"CD_name\" = \"\\U56fe\\U56fe\";\n    \"CD_role\" = daughter;\n    \"CD_updatedAt\" = \"2025-08-03 13:43:48 +0000\";\n    \"CD_user\" = \"61312E2B-2E12-496B-A31F-9024D36EF202\";\n}>",
    "<CKRecord: 0x10f43bf70; recordType=CD_PointRecord, recordID=E1AA39FD-5902-455E-8F2B-6EF7AFACCA39:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=t, values={\n    \"CD_entityName\" = PointRecord;\n    \"CD_id\" = \"35E7621A-7195-435E-BC45-458BDE2B33BB\";\n    \"CD_isReversed\" = 0;\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_reason\" = \"\\U5927\\U8f6c\\U76d8\\U62bd\\U5956\";\n    \"CD_recordType\" = lottery;\n    \"CD_timestamp\" = \"2025-08-03 08:31:59 +0000\";\n    \"CD_value\" = \"-1\";\n}>",
    "<CKRecord: 0x10f43c500; recordType=CD_MemberRule, recordID=EB6D71BC-D4F2-4BEC-A711-9F9F0888EBA8:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=s, values={\n    \"CD_createdAt\" = \"2025-08-03 08:30:41 +0000\";\n    \"CD_entityName\" = MemberRule;\n    \"CD_id\" = \"B01FA175-9BBF-46B8-B311-5B4C71C36D14\";\n    \"CD_isFrequent\" = 1;\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U4e0d\\U505a\\U5bb6\\U52a1\";\n    \"CD_type\" = deduct;\n    \"CD_value\" = 1;\n}>",
    "<CKRecord: 0x10f43c2e0; recordType=CD_MemberRule, recordID=4883E802-6336-4106-9738-458838769C24:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=r, values={\n    \"CD_createdAt\" = \"2025-08-03 08:29:56 +0000\";\n    \"CD_entityName\" = MemberRule;\n    \"CD_id\" = \"2CBA8884-00B4-4B6B-86D4-0587D9DF3153\";\n    \"CD_isFrequent\" = 1;\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U8ba4\\U771f\\U5b8c\\U6210\\U4f5c\\U4e1a\";\n    \"CD_type\" = add;\n    \"CD_value\" = 1;\n}>",
    "<CKRecord: 0x10f43c870; recordType=CD_MemberRule, recordID=FFEFFCD5-0868-40A9-8BD3-C1787221B97E:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=q, values={\n    \"CD_createdAt\" = \"2025-08-03 08:29:56 +0000\";\n    \"CD_entityName\" = MemberRule;\n    \"CD_id\" = \"8422969C-71CF-477E-B16B-1FFF39B3693F\";\n    \"CD_isFrequent\" = 1;\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U51c6\\U65f6\\U8d77\\U5e8a\";\n    \"CD_type\" = add;\n    \"CD_value\" = 1;\n}>",
    "<CKRecord: 0x10f43c650; recordType=CD_MemberRule, recordID=7C158E20-D34D-4D80-B0E3-109512DBF638:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=p, values={\n    \"CD_createdAt\" = \"2025-08-03 08:30:41 +0000\";\n    \"CD_entityName\" = MemberRule;\n    \"CD_id\" = \"DE7EE59B-4124-47AE-A1DD-2F23F61139E2\";\n    \"CD_isFrequent\" = 1;\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U9876\\U5634\";\n    \"CD_type\" = deduct;\n    \"CD_value\" = 1;\n}>",
    "<CKRecord: 0x10f43cbe0; recordType=CD_MemberRule, recordID=D652BDD3-D7B8-4292-A3FB-FDE9DA75722C:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=o, values={\n    \"CD_createdAt\" = \"2025-08-03 08:30:41 +0000\";\n    \"CD_entityName\" = MemberRule;\n    \"CD_id\" = \"427D462C-6D02-43DF-B791-2C422F197F3E\";\n    \"CD_isFrequent\" = 1;\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U4e0d\\U6536\\U62fe\\U623f\\U95f4\";\n    \"CD_type\" = deduct;\n    \"CD_value\" = 1;\n}>",
    "<CKRecord: 0x10f43c9c0; recordType=CD_MemberRule, recordID=ED2A98DC-2109-4927-99FC-EB797ACDB8FF:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=n, values={\n    \"CD_createdAt\" = \"2025-08-03 08:29:56 +0000\";\n    \"CD_entityName\" = MemberRule;\n    \"CD_id\" = \"E0E5767C-4E88-4D25-8D05-5F60B67CFC4D\";\n    \"CD_isFrequent\" = 1;\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U4e3b\\U52a8\\U6536\\U62fe\\U623f\\U95f4\";\n    \"CD_type\" = add;\n    \"CD_value\" = 1;\n}>",
    "<CKRecord: 0x10f43cf50; recordType=CD_MemberRule, recordID=285D75D1-FEF9-4847-856A-9651EE50DC9F:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=m, values={\n    \"CD_createdAt\" = \"2025-08-03 08:29:56 +0000\";\n    \"CD_entityName\" = MemberRule;\n    \"CD_id\" = \"0E1F8308-52D8-4CDA-8498-53D4095959EB\";\n    \"CD_isFrequent\" = 1;\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U80cc\\U4e00\\U9996\\U53e4\\U8bd7\";\n    \"CD_type\" = add;\n    \"CD_value\" = 1;\n}>",
    "<CKRecord: 0x10f43cd30; recordType=CD_MemberRule, recordID=8B55CD42-4E9F-407D-B26A-F4E386851DC8:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=l, values={\n    \"CD_createdAt\" = \"2025-08-03 08:30:41 +0000\";\n    \"CD_entityName\" = MemberRule;\n    \"CD_id\" = \"0CE40EF2-34B9-4D44-8DD1-65C54129BF28\";\n    \"CD_isFrequent\" = 1;\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U8d56\\U5e8a\";\n    \"CD_type\" = deduct;\n    \"CD_value\" = 1;\n}>",
    "<CKRecord: 0x10f43d2c0; recordType=CD_MemberRule, recordID=8CABBD82-B2C6-4BBE-A262-6EFFE69FDF23:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=k, values={\n    \"CD_createdAt\" = \"2025-08-03 08:29:56 +0000\";\n    \"CD_entityName\" = MemberRule;\n    \"CD_id\" = \"1C6BAEA4-9150-4519-B8C3-219650964251\";\n    \"CD_isFrequent\" = 1;\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U4e3b\\U52a8\\U5e2e\\U5fd9\\U505a\\U5bb6\\U52a1\";\n    \"CD_type\" = add;\n    \"CD_value\" = 1;\n}>",
    "<CKRecord: 0x10f43d0a0; recordType=CD_MemberRule, recordID=A8FEAAA8-EC76-4162-BA04-459D0C51BC74:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=j, values={\n    \"CD_createdAt\" = \"2025-08-03 08:30:41 +0000\";\n    \"CD_entityName\" = MemberRule;\n    \"CD_id\" = \"E91936ED-DA40-42E4-A3DF-FFE0D9F0CB1D\";\n    \"CD_isFrequent\" = 1;\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U4e0d\\U5199\\U4f5c\\U4e1a\";\n    \"CD_type\" = deduct;\n    \"CD_value\" = 1;\n}>",
    "<CKRecord: 0x10f43d630; recordType=CD_MemberPrize, recordID=60098737-99D8-438C-A18E-C7A38C560261:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=i, values={\n    \"CD_cost\" = 1;\n    \"CD_createdAt\" = \"2025-08-03 08:31:10 +0000\";\n    \"CD_entityName\" = MemberPrize;\n    \"CD_id\" = \"CC9B4BC5-AFFD-4B08-9A28-90153FA4DA35\";\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U5706\\U73e0\\U7b14\";\n}>",
    "<CKRecord: 0x10f43d9a0; recordType=CD_MemberPrize, recordID=0BFCB1B4-FFFE-4FAF-957F-3B987A07B905:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=h, values={\n    \"CD_cost\" = 1;\n    \"CD_createdAt\" = \"2025-08-03 08:31:10 +0000\";\n    \"CD_entityName\" = MemberPrize;\n    \"CD_id\" = \"654CB017-E3B3-405B-9357-50D524F80D7E\";\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U73a9\\U5177\";\n}>",
    "<CKRecord: 0x10f43daf0; recordType=CD_MemberPrize, recordID=8A639EBB-F806-4546-B7F9-234BBD50B619:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=g, values={\n    \"CD_cost\" = 1;\n    \"CD_createdAt\" = \"2025-08-03 08:31:10 +0000\";\n    \"CD_entityName\" = MemberPrize;\n    \"CD_id\" = \"A34FAB37-EBEC-4F17-B476-F6A0B312B302\";\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U6a61\\U76ae\";\n}>",
    "<CKRecord: 0x10f43bbd0; recordType=CD_MemberPrize, recordID=72785087-A1F8-4891-B612-4C89CAD8FF82:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=f, values={\n    \"CD_cost\" = 1;\n    \"CD_createdAt\" = \"2025-08-03 08:31:10 +0000\";\n    \"CD_entityName\" = MemberPrize;\n    \"CD_id\" = \"8469D771-330D-49AD-BD11-04DE2BFAF0F8\";\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_name\" = \"\\U94c5\\U7b14\";\n}>",
    "<CKRecord: 0x10f43d780; recordType=CD_LotteryRecord, recordID=D984E195-C660-44BA-B19E-842350170DB4:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=c, values={\n    \"CD_cost\" = 1;\n    \"CD_entityName\" = LotteryRecord;\n    \"CD_id\" = \"91E2B396-FDB0-41F7-AE3B-987BC39B784C\";\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_prizeResult\" = \"\\U96f6\\U98df\";\n    \"CD_timestamp\" = \"2025-08-03 08:31:59 +0000\";\n    \"CD_toolType\" = wheel;\n}>",
    "<CKRecord: 0x10f43dc40; recordType=CD_LotteryItem, recordID=5409779E-9C55-405D-9766-AC55BB529737:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=b, values={\n    \"CD_createdAt\" = \"2025-08-03 08:31:46 +0000\";\n    \"CD_entityName\" = LotteryItem;\n    \"CD_id\" = \"391C9695-5E26-4AEB-867F-529A8E7365C1\";\n    \"CD_itemIndex\" = 1;\n    \"CD_lotteryConfig\" = \"07994672-E560-4298-B713-1A164FDDB6C6\";\n    \"CD_prizeName\" = \"\\U6a61\\U76ae\";\n}>",
    "<CKRecord: 0x10f43d410; recordType=CD_LotteryItem, recordID=A641285A-BCF4-4793-B551-DD2334514DD2:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=a, values={\n    \"CD_createdAt\" = \"2025-08-03 08:31:46 +0000\";\n    \"CD_entityName\" = LotteryItem;\n    \"CD_id\" = \"E75402F7-4305-4AA6-A51F-A4498D62E7CD\";\n    \"CD_itemIndex\" = 3;\n    \"CD_lotteryConfig\" = \"07994672-E560-4298-B713-1A164FDDB6C6\";\n    \"CD_prizeName\" = \"\\U73a9\\U5177\";\n}>",
    "<CKRecord: 0x10f43dfb0; recordType=CD_LotteryItem, recordID=27E0E3A9-BDA3-4A5F-8ACA-CA08B5321878:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=9, values={\n    \"CD_createdAt\" = \"2025-08-03 08:31:46 +0000\";\n    \"CD_entityName\" = LotteryItem;\n    \"CD_id\" = \"F55C8C76-36BB-4171-AE08-71342055FA15\";\n    \"CD_itemIndex\" = 7;\n    \"CD_lotteryConfig\" = \"07994672-E560-4298-B713-1A164FDDB6C6\";\n    \"CD_prizeName\" = \"\\U5377\\U7b14\\U5200\";\n}>",
    "<CKRecord: 0x10f43e320; recordType=CD_LotteryItem, recordID=DA46954E-0E33-4D97-8F62-98329282F750:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=8, values={\n    \"CD_createdAt\" = \"2025-08-03 08:31:46 +0000\";\n    \"CD_entityName\" = LotteryItem;\n    \"CD_id\" = \"D882BAA7-B7E8-4E62-939C-16F367E99C02\";\n    \"CD_itemIndex\" = 5;\n    \"CD_lotteryConfig\" = \"07994672-E560-4298-B713-1A164FDDB6C6\";\n    \"CD_prizeName\" = \"\\U96f6\\U98df\";\n}>",
    "<CKRecord: 0x10f43e100; recordType=CD_LotteryItem, recordID=6DC254BF-390C-4B71-9F30-93EA2A9AFF52:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=7, values={\n    \"CD_createdAt\" = \"2025-08-03 08:31:46 +0000\";\n    \"CD_entityName\" = LotteryItem;\n    \"CD_id\" = \"63AED363-B4B7-4CAA-BD6A-0044A31FAA68\";\n    \"CD_itemIndex\" = 6;\n    \"CD_lotteryConfig\" = \"07994672-E560-4298-B713-1A164FDDB6C6\";\n    \"CD_prizeName\" = \"\\U5c3a\\U5b50\";\n}>",
    "<CKRecord: 0x10f43e470; recordType=CD_LotteryItem, recordID=F5A2307C-FEDE-4A51-9BFA-7143702082CE:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=6, values={\n    \"CD_createdAt\" = \"2025-08-03 08:31:46 +0000\";\n    \"CD_entityName\" = LotteryItem;\n    \"CD_id\" = \"C155AF0D-0680-4FA4-B576-E7FB5D9E2CD0\";\n    \"CD_itemIndex\" = 2;\n    \"CD_lotteryConfig\" = \"07994672-E560-4298-B713-1A164FDDB6C6\";\n    \"CD_prizeName\" = \"\\U7b14\\U8bb0\\U672c\";\n}>",
    "<CKRecord: 0x10f43e5c0; recordType=CD_LotteryItem, recordID=A013346D-E568-4499-9B82-3E8A027AC81B:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=5, values={\n    \"CD_createdAt\" = \"2025-08-03 08:31:46 +0000\";\n    \"CD_entityName\" = LotteryItem;\n    \"CD_id\" = \"57AAE969-317A-4042-8111-7EFF1657343F\";\n    \"CD_itemIndex\" = 4;\n    \"CD_lotteryConfig\" = \"07994672-E560-4298-B713-1A164FDDB6C6\";\n    \"CD_prizeName\" = \"\\U5361\\U7247\";\n}>",
    "<CKRecord: 0x10f43dd90; recordType=CD_LotteryItem, recordID=CE2C4740-5B26-40CD-87C5-87B97ABB0DF9:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=4, values={\n    \"CD_createdAt\" = \"2025-08-03 08:31:46 +0000\";\n    \"CD_entityName\" = LotteryItem;\n    \"CD_id\" = \"62325121-1871-41D4-BB2A-EF611DE92DB3\";\n    \"CD_itemIndex\" = 0;\n    \"CD_lotteryConfig\" = \"07994672-E560-4298-B713-1A164FDDB6C6\";\n    \"CD_prizeName\" = \"\\U94c5\\U7b14\";\n}>",
    "<CKRecord: 0x10f43e930; recordType=CD_LotteryConfig, recordID=07994672-E560-4298-B713-1A164FDDB6C6:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=3, values={\n    \"CD_costPerPlay\" = 1;\n    \"CD_createdAt\" = \"2025-08-03 08:31:46 +0000\";\n    \"CD_entityName\" = LotteryConfig;\n    \"CD_id\" = \"E581F220-98B6-462A-AE6E-5E474D625102\";\n    \"CD_itemCount\" = 8;\n    \"CD_member\" = \"1D4E85BD-6120-4DA8-86FA-CB60DAF22712\";\n    \"CD_toolType\" = wheel;\n    \"CD_updatedAt\" = \"2025-08-03 08:31:46 +0000\";\n}>"
)
Deleted RecordIDs:
{
    "CD_Subscription" =     (
        "<CKRecordID: 0x302f1de80; recordName=969EAF37-87E0-4F36-BA63-BE88675CF079, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>"
    );
    "CD_User" =     (
        "<CKRecordID: 0x302f1e700; recordName=0AB964DA-5F63-4070-A9BE-706872810E7C, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>"
    );
}
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext initializeCachesWithManagedObjectContext:andObservedStore:error:](191): Deleting record with id (CD_User): <CKRecordID: 0x302f1e700; recordName=0AB964DA-5F63-4070-A9BE-706872810E7C, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext initializeCachesWithManagedObjectContext:andObservedStore:error:](191): Deleting record with id (CD_Subscription): <CKRecordID: 0x302f1de80; recordName=969EAF37-87E0-4F36-BA63-BE88675CF079, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f10c20 <x-coredata:///User/tE096A66A-C06C-4F61-903D-E155C930942717> to <NSCKRecordMetadata: 0x300c47700> (entity: NSCKRecordMetadata; id: 0x302f11be0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942716>; data: {
    ckRecordName = "7C3C0B57-538C-42CF-B32B-E6D4A41874AD";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f12360; recordName=7C3C0B57-538C-42CF-B32B-E6D4A41874AD, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 982F49A1-B158-4C09-AF07-4C64CFC6AD39 by subscription
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f12040 <x-coredata:///Subscription/tE096A66A-C06C-4F61-903D-E155C930942719> to <NSCKRecordMetadata: 0x300c474d0> (entity: NSCKRecordMetadata; id: 0x302f12500 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942718>; data: {
    ckRecordName = "982F49A1-B158-4C09-AF07-4C64CFC6AD39";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f12800; recordName=982F49A1-B158-4C09-AF07-4C64CFC6AD39, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 7C3C0B57-538C-42CF-B32B-E6D4A41874AD by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff47c0 <x-coredata:///Member/tE096A66A-C06C-4F61-903D-E155C930942721> to <NSCKRecordMetadata: 0x300c4f1b0> (entity: NSCKRecordMetadata; id: 0x302ff4740 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942720>; data: {
    ckRecordName = "10AC02AB-D278-4432-9DCE-A17346D2EE06";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff5c80; recordName=10AC02AB-D278-4432-9DCE-A17346D2EE06, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 7C3C0B57-538C-42CF-B32B-E6D4A41874AD by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff5c00 <x-coredata:///Member/tE096A66A-C06C-4F61-903D-E155C930942723> to <NSCKRecordMetadata: 0x300c4e4e0> (entity: NSCKRecordMetadata; id: 0x302ff56c0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942722>; data: {
    ckRecordName = "87B69B0E-BCDA-4AA8-85BA-FB3113683C61";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff60a0; recordName=87B69B0E-BCDA-4AA8-85BA-FB3113683C61, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 7C3C0B57-538C-42CF-B32B-E6D4A41874AD by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff5800 <x-coredata:///GlobalRule/tE096A66A-C06C-4F61-903D-E155C930942725> to <NSCKRecordMetadata: 0x300c4e300> (entity: NSCKRecordMetadata; id: 0x302ff4780 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942724>; data: {
    ckRecordName = "3A3137E8-F5A1-4846-9698-7EB9A4D422F2";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffc0a0; recordName=3A3137E8-F5A1-4846-9698-7EB9A4D422F2, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 7C3C0B57-538C-42CF-B32B-E6D4A41874AD by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffc2a0 <x-coredata:///Member/tE096A66A-C06C-4F61-903D-E155C930942727> to <NSCKRecordMetadata: 0x300c55ef0> (entity: NSCKRecordMetadata; id: 0x302ffcb80 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942726>; data: {
    ckRecordName = "A85FFA0E-B82B-44D6-987C-16D8CD70361E";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffce40; recordName=A85FFA0E-B82B-44D6-987C-16D8CD70361E, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 2FC2E655-9FDE-4139-AA6D-D053B89CD6B2 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffcf80 <x-coredata:///User/tE096A66A-C06C-4F61-903D-E155C930942729> to <NSCKRecordMetadata: 0x300c54730> (entity: NSCKRecordMetadata; id: 0x302ffc7e0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942728>; data: {
    ckRecordName = "2FC2E655-9FDE-4139-AA6D-D053B89CD6B2";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffcdc0; recordName=2FC2E655-9FDE-4139-AA6D-D053B89CD6B2, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to DDAF8670-5B1D-41C2-B9B7-F7B508A54017 by subscription
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffc540 <x-coredata:///Subscription/tE096A66A-C06C-4F61-903D-E155C930942731> to <NSCKRecordMetadata: 0x300c57660> (entity: NSCKRecordMetadata; id: 0x302ffc4e0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942730>; data: {
    ckRecordName = "DDAF8670-5B1D-41C2-B9B7-F7B508A54017";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffc6e0; recordName=DDAF8670-5B1D-41C2-B9B7-F7B508A54017, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 2FC2E655-9FDE-4139-AA6D-D053B89CD6B2 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffd0a0 <x-coredata:///User/tE096A66A-C06C-4F61-903D-E155C930942733> to <NSCKRecordMetadata: 0x300c57a20> (entity: NSCKRecordMetadata; id: 0x302ffc820 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942732>; data: {
    ckRecordName = "6331FC0B-22EF-413A-91F3-2F53392B8227";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffcec0; recordName=6331FC0B-22EF-413A-91F3-2F53392B8227, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 6F774032-9AE8-4723-9857-DF96D823E9BE by subscription
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffd140 <x-coredata:///Subscription/tE096A66A-C06C-4F61-903D-E155C930942735> to <NSCKRecordMetadata: 0x300c58050> (entity: NSCKRecordMetadata; id: 0x302ffc420 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942734>; data: {
    ckRecordName = "6F774032-9AE8-4723-9857-DF96D823E9BE";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffd3c0; recordName=6F774032-9AE8-4723-9857-DF96D823E9BE, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 6331FC0B-22EF-413A-91F3-2F53392B8227 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffd440 <x-coredata:///Member/tE096A66A-C06C-4F61-903D-E155C930942737> to <NSCKRecordMetadata: 0x300c4bc00> (entity: NSCKRecordMetadata; id: 0x302ffce80 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942736>; data: {
    ckRecordName = "17E832A8-038A-4DB0-BD6D-494F828C9D70";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffc480; recordName=17E832A8-038A-4DB0-BD6D-494F828C9D70, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 6331FC0B-22EF-413A-91F3-2F53392B8227 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffd5a0 <x-coredata:///GlobalRule/tE096A66A-C06C-4F61-903D-E155C930942739> to <NSCKRecordMetadata: 0x300c73890> (entity: NSCKRecordMetadata; id: 0x302ffc620 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942738>; data: {
    ckRecordName = "EBB0DBEB-BE11-448A-9885-57B56EF716E4";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffd6e0; recordName=EBB0DBEB-BE11-448A-9885-57B56EF716E4, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 6331FC0B-22EF-413A-91F3-2F53392B8227 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f10e00 <x-coredata:///User/tE096A66A-C06C-4F61-903D-E155C930942741> to <NSCKRecordMetadata: 0x300c47ed0> (entity: NSCKRecordMetadata; id: 0x302f11560 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942740>; data: {
    ckRecordName = "180FA254-01F9-4240-933E-1209393BA6FF";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f12640; recordName=180FA254-01F9-4240-933E-1209393BA6FF, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 8F17728C-2CFE-48DE-BF59-71DF01394A46 by subscription
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f106e0 <x-coredata:///Subscription/tE096A66A-C06C-4F61-903D-E155C930942743> to <NSCKRecordMetadata: 0x300c4b750> (entity: NSCKRecordMetadata; id: 0x302f11060 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942742>; data: {
    ckRecordName = "8F17728C-2CFE-48DE-BF59-71DF01394A46";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f12a60; recordName=8F17728C-2CFE-48DE-BF59-71DF01394A46, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 180FA254-01F9-4240-933E-1209393BA6FF by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f12ae0 <x-coredata:///User/tE096A66A-C06C-4F61-903D-E155C930942745> to <NSCKRecordMetadata: 0x300c6c0a0> (entity: NSCKRecordMetadata; id: 0x302f11ee0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942744>; data: {
    ckRecordName = "53393E2A-44DB-4E56-A7AD-2FFF62744155";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f128e0; recordName=53393E2A-44DB-4E56-A7AD-2FFF62744155, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to E7B54033-B5D7-4AFC-86EF-6A0468EF06FD by subscription
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f12980 <x-coredata:///Subscription/tE096A66A-C06C-4F61-903D-E155C930942747> to <NSCKRecordMetadata: 0x300c47e80> (entity: NSCKRecordMetadata; id: 0x302f120c0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942746>; data: {
    ckRecordName = "E7B54033-B5D7-4AFC-86EF-6A0468EF06FD";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f12ca0; recordName=E7B54033-B5D7-4AFC-86EF-6A0468EF06FD, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 53393E2A-44DB-4E56-A7AD-2FFF62744155 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f12d20 <x-coredata:///Member/tE096A66A-C06C-4F61-903D-E155C930942749> to <NSCKRecordMetadata: 0x300c4bca0> (entity: NSCKRecordMetadata; id: 0x302f11ea0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942748>; data: {
    ckRecordName = "C80999E7-E183-46C7-A853-8210632E685A";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f10ce0; recordName=C80999E7-E183-46C7-A853-8210632E685A, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 53393E2A-44DB-4E56-A7AD-2FFF62744155 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f12ee0 <x-coredata:///Subscription/tE096A66A-C06C-4F61-903D-E155C930942751> to <NSCKRecordMetadata: 0x300c47de0> (entity: NSCKRecordMetadata; id: 0x302f11ca0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942750>; data: {
    ckRecordName = "ADD27B7A-A968-4E2F-847F-615C41CADD4B";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f129e0; recordName=ADD27B7A-A968-4E2F-847F-615C41CADD4B, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to F0754E26-EDD6-4A01-A0E4-12C5F1CDCF57 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f11540 <x-coredata:///User/tE096A66A-C06C-4F61-903D-E155C930942753> to <NSCKRecordMetadata: 0x300c4b7f0> (entity: NSCKRecordMetadata; id: 0x302f121e0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942752>; data: {
    ckRecordName = "E6314B9D-D265-4819-8147-B748B3E88EB4";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f12dc0; recordName=E6314B9D-D265-4819-8147-B748B3E88EB4, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 2916D1BA-20A4-48E1-B74F-B77690FADB7F by subscription
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f12620 <x-coredata:///Subscription/tE096A66A-C06C-4F61-903D-E155C930942755> to <NSCKRecordMetadata: 0x300c47b10> (entity: NSCKRecordMetadata; id: 0x302f12120 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942754>; data: {
    ckRecordName = "2916D1BA-20A4-48E1-B74F-B77690FADB7F";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f131e0; recordName=2916D1BA-20A4-48E1-B74F-B77690FADB7F, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to E6314B9D-D265-4819-8147-B748B3E88EB4 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f13260 <x-coredata:///Member/tE096A66A-C06C-4F61-903D-E155C930942757> to <NSCKRecordMetadata: 0x300c4ba70> (entity: NSCKRecordMetadata; id: 0x302f12840 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942756>; data: {
    ckRecordName = "07F38EFA-440C-434C-B9F3-BFC061054865";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f12be0; recordName=07F38EFA-440C-434C-B9F3-BFC061054865, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to E6314B9D-D265-4819-8147-B748B3E88EB4 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f13420 <x-coredata:///User/tE096A66A-C06C-4F61-903D-E155C930942759> to <NSCKRecordMetadata: 0x300c47bb0> (entity: NSCKRecordMetadata; id: 0x302f12260 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942758>; data: {
    ckRecordName = "F0754E26-EDD6-4A01-A0E4-12C5F1CDCF57";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f13100; recordName=F0754E26-EDD6-4A01-A0E4-12C5F1CDCF57, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to ADD27B7A-A968-4E2F-847F-615C41CADD4B by subscription
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f13460 <x-coredata:///Member/tE096A66A-C06C-4F61-903D-E155C930942761> to <NSCKRecordMetadata: 0x300c4b890> (entity: NSCKRecordMetadata; id: 0x302f12340 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942760>; data: {
    ckRecordName = "3BFA499F-5D63-4B09-A197-09F03A26EE27";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f13680; recordName=3BFA499F-5D63-4B09-A197-09F03A26EE27, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 61312E2B-2E12-496B-A31F-9024D36EF202 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f137c0 <x-coredata:///User/tE096A66A-C06C-4F61-903D-E155C930942763> to <NSCKRecordMetadata: 0x300c47a20> (entity: NSCKRecordMetadata; id: 0x302f131c0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942762>; data: {
    ckRecordName = "61312E2B-2E12-496B-A31F-9024D36EF202";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f13800; recordName=61312E2B-2E12-496B-A31F-9024D36EF202, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to E985AF71-C5FD-41E2-A15E-46EE3F71859E by subscription
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f11fe0 <x-coredata:///Member/tE096A66A-C06C-4F61-903D-E155C930942765> to <NSCKRecordMetadata: 0x300c4bf70> (entity: NSCKRecordMetadata; id: 0x302f119a0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942764>; data: {
    ckRecordName = "1D4E85BD-6120-4DA8-86FA-CB60DAF22712";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f139e0; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 61312E2B-2E12-496B-A31F-9024D36EF202 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f13b20 <x-coredata:///Member/tE096A66A-C06C-4F61-903D-E155C930942767> to <NSCKRecordMetadata: 0x300c477a0> (entity: NSCKRecordMetadata; id: 0x302f13380 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942766>; data: {
    ckRecordName = "34700A9F-4B97-43B0-9F47-C76B69A2A22C";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f13be0; recordName=34700A9F-4B97-43B0-9F47-C76B69A2A22C, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 61312E2B-2E12-496B-A31F-9024D36EF202 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f13d20 <x-coredata:///Subscription/tE096A66A-C06C-4F61-903D-E155C930942769> to <NSCKRecordMetadata: 0x300c4b020> (entity: NSCKRecordMetadata; id: 0x302f137a0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942768>; data: {
    ckRecordName = "E985AF71-C5FD-41E2-A15E-46EE3F71859E";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f13de0; recordName=E985AF71-C5FD-41E2-A15E-46EE3F71859E, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 61312E2B-2E12-496B-A31F-9024D36EF202 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302f13e60 <x-coredata:///Member/tE096A66A-C06C-4F61-903D-E155C930942771> to <NSCKRecordMetadata: 0x300c47750> (entity: NSCKRecordMetadata; id: 0x302f13720 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942770>; data: {
    ckRecordName = "82107A21-A6E2-4D21-95F9-5878BB7730A9";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f188e0; recordName=82107A21-A6E2-4D21-95F9-5878BB7730A9, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 61312E2B-2E12-496B-A31F-9024D36EF202 by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffc860 <x-coredata:///PointRecord/tE096A66A-C06C-4F61-903D-E155C930942773> to <NSCKRecordMetadata: 0x300c4b4d0> (entity: NSCKRecordMetadata; id: 0x302f1efc0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942772>; data: {
    ckRecordName = "E1AA39FD-5902-455E-8F2B-6EF7AFACCA39";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffd760; recordName=E1AA39FD-5902-455E-8F2B-6EF7AFACCA39, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffd5e0 <x-coredata:///MemberRule/tE096A66A-C06C-4F61-903D-E155C930942775> to <NSCKRecordMetadata: 0x300c475c0> (entity: NSCKRecordMetadata; id: 0x302ffd880 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942774>; data: {
    ckRecordName = "EB6D71BC-D4F2-4BEC-A711-9F9F0888EBA8";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffdd60; recordName=EB6D71BC-D4F2-4BEC-A711-9F9F0888EBA8, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffdce0 <x-coredata:///MemberRule/tE096A66A-C06C-4F61-903D-E155C930942777> to <NSCKRecordMetadata: 0x300c43a70> (entity: NSCKRecordMetadata; id: 0x302ffd8e0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942776>; data: {
    ckRecordName = "4883E802-6336-4106-9738-458838769C24";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff8c40; recordName=4883E802-6336-4106-9738-458838769C24, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff89e0 <x-coredata:///MemberRule/tE096A66A-C06C-4F61-903D-E155C930942779> to <NSCKRecordMetadata: 0x300c44500> (entity: NSCKRecordMetadata; id: 0x302ff8f20 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942778>; data: {
    ckRecordName = "FFEFFCD5-0868-40A9-8BD3-C1787221B97E";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff90e0; recordName=FFEFFCD5-0868-40A9-8BD3-C1787221B97E, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff9060 <x-coredata:///MemberRule/tE096A66A-C06C-4F61-903D-E155C930942781> to <NSCKRecordMetadata: 0x300c5fd90> (entity: NSCKRecordMetadata; id: 0x302ff85e0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942780>; data: {
    ckRecordName = "7C158E20-D34D-4D80-B0E3-109512DBF638";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff91e0; recordName=7C158E20-D34D-4D80-B0E3-109512DBF638, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff9020 <x-coredata:///MemberRule/tE096A66A-C06C-4F61-903D-E155C930942783> to <NSCKRecordMetadata: 0x300c73f20> (entity: NSCKRecordMetadata; id: 0x302ff8f60 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942782>; data: {
    ckRecordName = "D652BDD3-D7B8-4292-A3FB-FDE9DA75722C";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff92e0; recordName=D652BDD3-D7B8-4292-A3FB-FDE9DA75722C, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff86e0 <x-coredata:///MemberRule/tE096A66A-C06C-4F61-903D-E155C930942785> to <NSCKRecordMetadata: 0x300c5efd0> (entity: NSCKRecordMetadata; id: 0x302ff8b80 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942784>; data: {
    ckRecordName = "ED2A98DC-2109-4927-99FC-EB797ACDB8FF";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff93e0; recordName=ED2A98DC-2109-4927-99FC-EB797ACDB8FF, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff8560 <x-coredata:///MemberRule/tE096A66A-C06C-4F61-903D-E155C930942787> to <NSCKRecordMetadata: 0x300c73ac0> (entity: NSCKRecordMetadata; id: 0x302ff8e20 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942786>; data: {
    ckRecordName = "285D75D1-FEF9-4847-856A-9651EE50DC9F";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffd220; recordName=285D75D1-FEF9-4847-856A-9651EE50DC9F, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff94e0 <x-coredata:///MemberRule/tE096A66A-C06C-4F61-903D-E155C930942789> to <NSCKRecordMetadata: 0x300c5f110> (entity: NSCKRecordMetadata; id: 0x302ff8a40 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942788>; data: {
    ckRecordName = "8B55CD42-4E9F-407D-B26A-F4E386851DC8";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff9580; recordName=8B55CD42-4E9F-407D-B26A-F4E386851DC8, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff9240 <x-coredata:///MemberRule/tE096A66A-C06C-4F61-903D-E155C930942791> to <NSCKRecordMetadata: 0x300c6f2f0> (entity: NSCKRecordMetadata; id: 0x302ff9000 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942790>; data: {
    ckRecordName = "8CABBD82-B2C6-4BBE-A262-6EFFE69FDF23";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff96a0; recordName=8CABBD82-B2C6-4BBE-A262-6EFFE69FDF23, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff89a0 <x-coredata:///MemberRule/tE096A66A-C06C-4F61-903D-E155C930942793> to <NSCKRecordMetadata: 0x300c58280> (entity: NSCKRecordMetadata; id: 0x302ff8f00 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942792>; data: {
    ckRecordName = "A8FEAAA8-EC76-4162-BA04-459D0C51BC74";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff97a0; recordName=A8FEAAA8-EC76-4162-BA04-459D0C51BC74, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff8600 <x-coredata:///MemberPrize/tE096A66A-C06C-4F61-903D-E155C930942795> to <NSCKRecordMetadata: 0x300c563a0> (entity: NSCKRecordMetadata; id: 0x302ff8f80 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942794>; data: {
    ckRecordName = "60098737-99D8-438C-A18E-C7A38C560261";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff9b40; recordName=60098737-99D8-438C-A18E-C7A38C560261, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff9ac0 <x-coredata:///MemberPrize/tE096A66A-C06C-4F61-903D-E155C930942797> to <NSCKRecordMetadata: 0x300c5fc00> (entity: NSCKRecordMetadata; id: 0x302ff9800 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942796>; data: {
    ckRecordName = "0BFCB1B4-FFFE-4FAF-957F-3B987A07B905";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff9c60; recordName=0BFCB1B4-FFFE-4FAF-957F-3B987A07B905, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff9b60 <x-coredata:///MemberPrize/tE096A66A-C06C-4F61-903D-E155C930942799> to <NSCKRecordMetadata: 0x300c57430> (entity: NSCKRecordMetadata; id: 0x302ff8500 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C930942798>; data: {
    ckRecordName = "8A639EBB-F806-4546-B7F9-234BBD50B619";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff9d60; recordName=8A639EBB-F806-4546-B7F9-234BBD50B619, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff9ca0 <x-coredata:///MemberPrize/tE096A66A-C06C-4F61-903D-E155C9309427101> to <NSCKRecordMetadata: 0x300c5ed50> (entity: NSCKRecordMetadata; id: 0x302ff8880 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C9309427100>; data: {
    ckRecordName = "72785087-A1F8-4891-B612-4C89CAD8FF82";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ff9e60; recordName=72785087-A1F8-4891-B612-4C89CAD8FF82, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff9320 <x-coredata:///LotteryRecord/tE096A66A-C06C-4F61-903D-E155C9309427103> to <NSCKRecordMetadata: 0x300c57480> (entity: NSCKRecordMetadata; id: 0x302ff9200 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C9309427102>; data: {
    ckRecordName = "D984E195-C660-44BA-B19E-842350170DB4";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffa280; recordName=D984E195-C660-44BA-B19E-842350170DB4, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ff9ec0 <x-coredata:///LotteryItem/tE096A66A-C06C-4F61-903D-E155C9309427105> to <NSCKRecordMetadata: 0x300c73bb0> (entity: NSCKRecordMetadata; id: 0x302ff9a00 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C9309427104>; data: {
    ckRecordName = "5409779E-9C55-405D-9766-AC55BB529737";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffa5e0; recordName=5409779E-9C55-405D-9766-AC55BB529737, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 07994672-E560-4298-B713-1A164FDDB6C6 by lotteryConfig
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffa660 <x-coredata:///LotteryItem/tE096A66A-C06C-4F61-903D-E155C9309427107> to <NSCKRecordMetadata: 0x300c2f570> (entity: NSCKRecordMetadata; id: 0x302ff8fa0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C9309427106>; data: {
    ckRecordName = "A641285A-BCF4-4793-B551-DD2334514DD2";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffa680; recordName=A641285A-BCF4-4793-B551-DD2334514DD2, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 07994672-E560-4298-B713-1A164FDDB6C6 by lotteryConfig
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffa7a0 <x-coredata:///LotteryItem/tE096A66A-C06C-4F61-903D-E155C9309427109> to <NSCKRecordMetadata: 0x300c5e3a0> (entity: NSCKRecordMetadata; id: 0x302ff9ea0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C9309427108>; data: {
    ckRecordName = "27E0E3A9-BDA3-4A5F-8ACA-CA08B5321878";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffa760; recordName=27E0E3A9-BDA3-4A5F-8ACA-CA08B5321878, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 07994672-E560-4298-B713-1A164FDDB6C6 by lotteryConfig
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffa8a0 <x-coredata:///LotteryItem/tE096A66A-C06C-4F61-903D-E155C9309427111> to <NSCKRecordMetadata: 0x300c2c0f0> (entity: NSCKRecordMetadata; id: 0x302ffa040 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C9309427110>; data: {
    ckRecordName = "DA46954E-0E33-4D97-8F62-98329282F750";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffa720; recordName=DA46954E-0E33-4D97-8F62-98329282F750, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 07994672-E560-4298-B713-1A164FDDB6C6 by lotteryConfig
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffa9c0 <x-coredata:///LotteryItem/tE096A66A-C06C-4F61-903D-E155C9309427113> to <NSCKRecordMetadata: 0x300c452c0> (entity: NSCKRecordMetadata; id: 0x302ff8c60 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C9309427112>; data: {
    ckRecordName = "6DC254BF-390C-4B71-9F30-93EA2A9AFF52";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffa860; recordName=6DC254BF-390C-4B71-9F30-93EA2A9AFF52, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 07994672-E560-4298-B713-1A164FDDB6C6 by lotteryConfig
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffaac0 <x-coredata:///LotteryItem/tE096A66A-C06C-4F61-903D-E155C9309427115> to <NSCKRecordMetadata: 0x300c3f250> (entity: NSCKRecordMetadata; id: 0x302ffa020 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C9309427114>; data: {
    ckRecordName = "F5A2307C-FEDE-4A51-9BFA-7143702082CE";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffa960; recordName=F5A2307C-FEDE-4A51-9BFA-7143702082CE, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 07994672-E560-4298-B713-1A164FDDB6C6 by lotteryConfig
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffaba0 <x-coredata:///LotteryItem/tE096A66A-C06C-4F61-903D-E155C9309427117> to <NSCKRecordMetadata: 0x300c36fd0> (entity: NSCKRecordMetadata; id: 0x302ffa940 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C9309427116>; data: {
    ckRecordName = "A013346D-E568-4499-9B82-3E8A027AC81B";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffaa80; recordName=A013346D-E568-4499-9B82-3E8A027AC81B, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 07994672-E560-4298-B713-1A164FDDB6C6 by lotteryConfig
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffaca0 <x-coredata:///LotteryItem/tE096A66A-C06C-4F61-903D-E155C9309427119> to <NSCKRecordMetadata: 0x300c73ca0> (entity: NSCKRecordMetadata; id: 0x302ffa6c0 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C9309427118>; data: {
    ckRecordName = "CE2C4740-5B26-40CD-87C5-87B97ABB0DF9";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffab80; recordName=CE2C4740-5B26-40CD-87C5-87B97ABB0DF9, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 07994672-E560-4298-B713-1A164FDDB6C6 by lotteryConfig
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext registerObject:forInsertedRecord:withMetadata:](497): Linking 0x302ffa980 <x-coredata:///LotteryConfig/tE096A66A-C06C-4F61-903D-E155C9309427121> to <NSCKRecordMetadata: 0x300c335c0> (entity: NSCKRecordMetadata; id: 0x302ffaa20 <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordMetadata/tE096A66A-C06C-4F61-903D-E155C9309427120>; data: {
    ckRecordName = "07994672-E560-4298-B713-1A164FDDB6C6";
    ckRecordSystemFields = nil;
    ckShare = nil;
    encodedRecord = nil;
    entityId = nil;
    entityPK = nil;
    lastExportedTransactionNumber = nil;
    moveReceipts =     (
    );
    needsCloudDelete = 0;
    needsLocalDelete = 0;
    needsUpload = 0;
    pendingExportChangeTypeNumber = nil;
    pendingExportTransactionNumber = nil;
    recordZone = "0x95726ce5d347b8aa <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/NSCKRecordZoneMetadata/p1>";
})
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302ffb000; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 1D4E85BD-6120-4DA8-86FA-CB60DAF22712 by member
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 2;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464820>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext populateUnresolvedIDsInStore:withManagedObjectContext:error:]_block_invoke(551): Populating unresolved relationships:
{
    LotteryConfig =     (
        "<CKRecordID: 0x302ffa3e0; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302ffa3c0; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302ffa380; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302ffa1e0; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302ffa4c0; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302ffa400; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302ff9e20; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302ffa440; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>"
    );
    Subscription =     (
        "<CKRecordID: 0x302f122c0; recordName=982F49A1-B158-4C09-AF07-4C64CFC6AD39, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302ffcf20; recordName=DDAF8670-5B1D-41C2-B9B7-F7B508A54017, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302ffd020; recordName=6F774032-9AE8-4723-9857-DF96D823E9BE, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302f12740; recordName=8F17728C-2CFE-48DE-BF59-71DF01394A46, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302f12a20; recordName=E7B54033-B5D7-4AFC-86EF-6A0468EF06FD, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302f12960; recordName=2916D1BA-20A4-48E1-B74F-B77690FADB7F, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302f13620; recordName=E985AF71-C5FD-41E2-A15E-46EE3F71859E, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>"
    );
    User =     (
        "<CKRecordID: 0x302ffcaa0; recordName=2FC2E655-9FDE-4139-AA6D-D053B89CD6B2, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302f12d80; recordName=F0754E26-EDD6-4A01-A0E4-12C5F1CDCF57, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>",
        "<CKRecordID: 0x302f12fe0; recordName=61312E2B-2E12-496B-A31F-9024D36EF202, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__>"
    );
}
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d4270>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f12360; recordName=7C3C0B57-538C-42CF-B32B-E6D4A41874AD, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f122c0; recordName=982F49A1-B158-4C09-AF07-4C64CFC6AD39, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via subscription on 0x95726ce5d3e7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p4>->0x95726ce5d3c7b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p5>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d4540>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f12800; recordName=982F49A1-B158-4C09-AF07-4C64CFC6AD39, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f122a0; recordName=7C3C0B57-538C-42CF-B32B-E6D4A41874AD, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d3c7b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p5>->0x95726ce5d3e7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p4>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021df1e0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff5c80; recordName=10AC02AB-D278-4432-9DCE-A17346D2EE06, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff4940; recordName=7C3C0B57-538C-42CF-B32B-E6D4A41874AD, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d347b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p1>->0x95726ce5d3e7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p4>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021df7e0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff60a0; recordName=87B69B0E-BCDA-4AA8-85BA-FB3113683C61, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff5960; recordName=7C3C0B57-538C-42CF-B32B-E6D4A41874AD, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d267b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p8>->0x95726ce5d3e7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p4>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021fc9c0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffc0a0; recordName=3A3137E8-F5A1-4846-9698-7EB9A4D422F2, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffc0c0; recordName=7C3C0B57-538C-42CF-B32B-E6D4A41874AD, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d327b82a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/GlobalRule/p2>->0x95726ce5d3e7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p4>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021fdbf0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffce40; recordName=A85FFA0E-B82B-44D6-987C-16D8CD70361E, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffcaa0; recordName=2FC2E655-9FDE-4139-AA6D-D053B89CD6B2, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d3a7b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p6>->0x95726ce5d307b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p3>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021fc840>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffcdc0; recordName=2FC2E655-9FDE-4139-AA6D-D053B89CD6B2, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffcf20; recordName=DDAF8670-5B1D-41C2-B9B7-F7B508A54017, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via subscription on 0x95726ce5d307b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p3>->0x95726ce5d3a7b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p6>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021ff210>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffc6e0; recordName=DDAF8670-5B1D-41C2-B9B7-F7B508A54017, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffcde0; recordName=2FC2E655-9FDE-4139-AA6D-D053B89CD6B2, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d3a7b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p6>->0x95726ce5d307b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p3>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021fcb10>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffcec0; recordName=6331FC0B-22EF-413A-91F3-2F53392B8227, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffd020; recordName=6F774032-9AE8-4723-9857-DF96D823E9BE, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via subscription on 0x95726ce5d387b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p7>->0x95726ce5d387b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x30210ff60>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffd3c0; recordName=6F774032-9AE8-4723-9857-DF96D823E9BE, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffc080; recordName=6331FC0B-22EF-413A-91F3-2F53392B8227, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d387b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p7>->0x95726ce5d387b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021fe430>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffc480; recordName=17E832A8-038A-4DB0-BD6D-494F828C9D70, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffcee0; recordName=6331FC0B-22EF-413A-91F3-2F53392B8227, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d327b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p2>->0x95726ce5d387b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d4330>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffd6e0; recordName=EBB0DBEB-BE11-448A-9885-57B56EF716E4, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffd6a0; recordName=6331FC0B-22EF-413A-91F3-2F53392B8227, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d347b82a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/GlobalRule/p1>->0x95726ce5d387b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d4e40>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f12640; recordName=180FA254-01F9-4240-933E-1209393BA6FF, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f12740; recordName=8F17728C-2CFE-48DE-BF59-71DF01394A46, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via subscription on 0x95726ce5d267b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p8>->0x95726ce5d267b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p8>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d4f60>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f12a60; recordName=8F17728C-2CFE-48DE-BF59-71DF01394A46, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f12600; recordName=180FA254-01F9-4240-933E-1209393BA6FF, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d267b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p8>->0x95726ce5d267b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p8>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d4ea0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f128e0; recordName=53393E2A-44DB-4E56-A7AD-2FFF62744155, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f12a20; recordName=E7B54033-B5D7-4AFC-86EF-6A0468EF06FD, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via subscription on 0x95726ce5d3c7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p5>->0x95726ce5d3e7b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p4>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d45d0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f12ca0; recordName=E7B54033-B5D7-4AFC-86EF-6A0468EF06FD, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f11ac0; recordName=53393E2A-44DB-4E56-A7AD-2FFF62744155, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d3e7b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p4>->0x95726ce5d3c7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p5>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d4a50>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f10ce0; recordName=C80999E7-E183-46C7-A853-8210632E685A, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f126a0; recordName=53393E2A-44DB-4E56-A7AD-2FFF62744155, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d307b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p3>->0x95726ce5d3c7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p5>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d5020>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f129e0; recordName=ADD27B7A-A968-4E2F-847F-615C41CADD4B, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f12d80; recordName=F0754E26-EDD6-4A01-A0E4-12C5F1CDCF57, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d247b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p9>->0x95726ce5d247b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p9>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d5740>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f12dc0; recordName=E6314B9D-D265-4819-8147-B748B3E88EB4, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f12960; recordName=2916D1BA-20A4-48E1-B74F-B77690FADB7F, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via subscription on 0x95726ce5d327b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p2>->0x95726ce5d307b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p3>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d51a0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f131e0; recordName=2916D1BA-20A4-48E1-B74F-B77690FADB7F, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f11460; recordName=E6314B9D-D265-4819-8147-B748B3E88EB4, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d307b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p3>->0x95726ce5d327b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p2>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d4cc0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f12be0; recordName=07F38EFA-440C-434C-B9F3-BFC061054865, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f10ea0; recordName=E6314B9D-D265-4819-8147-B748B3E88EB4, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d3c7b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p5>->0x95726ce5d327b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p2>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d5c80>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f13100; recordName=F0754E26-EDD6-4A01-A0E4-12C5F1CDCF57, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f13340; recordName=ADD27B7A-A968-4E2F-847F-615C41CADD4B, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via subscription on 0x95726ce5d247b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p9>->0x95726ce5d247b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p9>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d5e30>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f13680; recordName=3BFA499F-5D63-4B09-A197-09F03A26EE27, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f12fe0; recordName=61312E2B-2E12-496B-A31F-9024D36EF202, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d247b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p9>->0x95726ce5d3a7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p6>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d5d70>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f13800; recordName=61312E2B-2E12-496B-A31F-9024D36EF202, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f13620; recordName=E985AF71-C5FD-41E2-A15E-46EE3F71859E, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via subscription on 0x95726ce5d3a7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p6>->0x95726ce5d327b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p2>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d6220>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f139e0; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f11c80; recordName=61312E2B-2E12-496B-A31F-9024D36EF202, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>->0x95726ce5d3a7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p6>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d64c0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f13be0; recordName=34700A9F-4B97-43B0-9F47-C76B69A2A22C, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f12920; recordName=61312E2B-2E12-496B-A31F-9024D36EF202, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d227b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p10>->0x95726ce5d3a7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p6>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d6730>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f13de0; recordName=E985AF71-C5FD-41E2-A15E-46EE3F71859E, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f13d40; recordName=61312E2B-2E12-496B-A31F-9024D36EF202, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d327b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p2>->0x95726ce5d3a7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p6>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021d6520>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f188e0; recordName=82107A21-A6E2-4D21-95F9-5878BB7730A9, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f1b9c0; recordName=61312E2B-2E12-496B-A31F-9024D36EF202, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d3e7b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p4>->0x95726ce5d3a7b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p6>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021fc210>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffd760; recordName=E1AA39FD-5902-455E-8F2B-6EF7AFACCA39, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffcc60; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d347b99a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/PointRecord/p1>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c5530>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffdd60; recordName=EB6D71BC-D4F2-4BEC-A711-9F9F0888EBA8, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffdd40; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d227b98a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberRule/p10>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c05d0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff8c40; recordName=4883E802-6336-4106-9738-458838769C24, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff8c20; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d267b98a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberRule/p8>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c0fc0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff90e0; recordName=FFEFFCD5-0868-40A9-8BD3-C1787221B97E, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff90c0; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d307b98a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberRule/p3>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c0d20>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff91e0; recordName=7C158E20-D34D-4D80-B0E3-109512DBF638, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff91c0; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d3e7b98a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberRule/p4>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c0d50>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff92e0; recordName=D652BDD3-D7B8-4292-A3FB-FDE9DA75722C, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff92c0; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d327b98a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberRule/p2>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c0cc0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff93e0; recordName=ED2A98DC-2109-4927-99FC-EB797ACDB8FF, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff93c0; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d3a7b98a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberRule/p6>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c1560>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffd220; recordName=285D75D1-FEF9-4847-856A-9651EE50DC9F, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffd700; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d247b98a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberRule/p9>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c0600>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff9580; recordName=8B55CD42-4E9F-407D-B26A-F4E386851DC8, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff8960; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d387b98a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberRule/p7>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c1140>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff96a0; recordName=8CABBD82-B2C6-4BBE-A262-6EFFE69FDF23, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff9680; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d3c7b98a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberRule/p5>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c1260>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff97a0; recordName=A8FEAAA8-EC76-4162-BA04-459D0C51BC74, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff9780; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d347b98a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberRule/p1>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c1a70>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff9b40; recordName=60098737-99D8-438C-A18E-C7A38C560261, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff9b20; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d327b87a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberPrize/p2>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c18f0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff9c60; recordName=0BFCB1B4-FFFE-4FAF-957F-3B987A07B905, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff9c40; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d347b87a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberPrize/p1>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c1a40>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff9d60; recordName=8A639EBB-F806-4546-B7F9-234BBD50B619, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff9d40; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d307b87a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberPrize/p3>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c1290>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ff9e60; recordName=72785087-A1F8-4891-B612-4C89CAD8FF82, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff9e40; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d3e7b87a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/MemberPrize/p4>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c20a0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffa280; recordName=D984E195-C660-44BA-B19E-842350170DB4, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffa220; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d347b85a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryRecord/p1>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c24c0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffa5e0; recordName=5409779E-9C55-405D-9766-AC55BB529737, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffa3e0; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via lotteryConfig on 0x95726ce5d327b84a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryItem/p2>->0x95726ce5d347b83a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryConfig/p1>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c2610>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffa680; recordName=A641285A-BCF4-4793-B551-DD2334514DD2, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffa3c0; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via lotteryConfig on 0x95726ce5d387b84a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryItem/p7>->0x95726ce5d347b83a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryConfig/p1>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c2730>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffa760; recordName=27E0E3A9-BDA3-4A5F-8ACA-CA08B5321878, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffa380; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via lotteryConfig on 0x95726ce5d3a7b84a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryItem/p6>->0x95726ce5d347b83a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryConfig/p1>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c1f50>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffa720; recordName=DA46954E-0E33-4D97-8F62-98329282F750, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffa1e0; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via lotteryConfig on 0x95726ce5d267b84a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryItem/p8>->0x95726ce5d347b83a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryConfig/p1>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c2940>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffa860; recordName=6DC254BF-390C-4B71-9F30-93EA2A9AFF52, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffa4c0; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via lotteryConfig on 0x95726ce5d3c7b84a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryItem/p5>->0x95726ce5d347b83a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryConfig/p1>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c2a90>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffa960; recordName=F5A2307C-FEDE-4A51-9BFA-7143702082CE, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffa400; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via lotteryConfig on 0x95726ce5d347b84a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryItem/p1>->0x95726ce5d347b83a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryConfig/p1>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c2bb0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffaa80; recordName=A013346D-E568-4499-9B82-3E8A027AC81B, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ff9e20; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via lotteryConfig on 0x95726ce5d3e7b84a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryItem/p4>->0x95726ce5d347b83a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryConfig/p1>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c2250>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffab80; recordName=CE2C4740-5B26-40CD-87C5-87B97ABB0DF9, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffa440; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via lotteryConfig on 0x95726ce5d307b84a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryItem/p3>->0x95726ce5d347b83a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryConfig/p1>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c2e80>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302ffb000; recordName=07994672-E560-4298-B713-1A164FDDB6C6, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302ffb160; recordName=1D4E85BD-6120-4DA8-86FA-CB60DAF22712, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via member on 0x95726ce5d347b83a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/LotteryConfig/p1>->0x95726ce5d387b86a <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Member/p7>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464820>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464820>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464820>
CoreData: warning: CoreData+CloudKit: -[PFCloudKitImportRecordsWorkItem applyAccumulatedChangesToStore:inManagedObjectContext:withStoreMonitor:madeChanges:error:](513): Finished importing applying changes for request: <NSCloudKitMirroringImportRequest: 0x300c4b520> 48E34981-914B-4BA0-9294-D661512E8C64
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464820>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464820>
CoreData: warning: CoreData+CloudKit: -[PFCloudKitImportRecordsWorkItem newMirroringResultByApplyingAccumulatedChanges]_block_invoke(241): Finished importing changes for request: <NSCloudKitMirroringImportRequest: 0x300c4b520> 48E34981-914B-4BA0-9294-D661512E8C64
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitImporter processWorkItemsWithCompletion:](418): <PFCloudKitImporter: 0x303a27540>: Processing work items: (
)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30146c4e0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30146c8f0>
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _finishedRequest:withResult:](3566): Finished request: <NSCloudKitMirroringImportRequest: 0x300c4b520> 48E34981-914B-4BA0-9294-D661512E8C64 with result: <NSCloudKitMirroringResult: 0x3021fa850> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 1 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedImportWithLabel:activity:voucher:completionHandler:]_block_invoke(3633): <NSCloudKitMirroringDelegate: 0x301060000> - Finished automatic import - SandboxImport - with result: <NSCloudKitMirroringResult: 0x3021fa850> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 1 error: (null)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate finishedAutomatedRequestWithResult:](3579): Finished request '<NSCloudKitMirroringImportRequest: 0x300c4b520> 48E34981-914B-4BA0-9294-D661512E8C64' with result: <NSCloudKitMirroringResult: 0x3021fa850> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 1 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndScheduleImportIfNecessaryFromPush:fromErrorRecovery:fromShareAccept:andStartAfterDate:]_block_invoke_2(3314): <NSCloudKitMirroringDelegate: 0x301060000>: Checking to see if an automated import should be scheduled.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3548): <NSCloudKitMirroringDelegate: 0x301060000>: Executing: <NSCloudKitMirroringExportRequest: 0x300c4a800> CE352D4A-BF70-477F-A7E2-8DD73C07FED5
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30146c9c0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30146c9c0>
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter analyzeHistoryInStore:withManagedObjectContext:error:](531): <PFCloudKitExporter: 0x300c3a080>: Exporting changes since (0): (null)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30146c9c0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30146c9c0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExportContext processAnalyzedHistoryInStore:inManagedObjectContext:error:]_block_invoke_5(251): Finished processing analyzed history with 2 metadata objects to create, 0 deleted rows without metadata.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30146c9c0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30146c9c0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30146c9c0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter exportIfNecessary]_block_invoke_2(345): <PFCloudKitExporter: 0x300c3a080>: Found 2 objects needing export.
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer newCKRecordsFromObject:fullyMaterializeRecords:includeRelationships:error:](576): Serializer has finished creating record: <CKRecord: 0x10de69190; recordType=CD_Subscription, recordID=9EDF0DE8-A847-48E9-BA6E-82F18EA8F48F:(com.apple.coredata.cloudkit.zone:__defaultOwner__), values={
    "CD_createdAt" = "2025-08-03 16:07:52 +0000";
    "CD_entityName" = Subscription;
    "CD_id" = "6A45A1E9-19DE-42A0-BD65-D68217624D2E";
    "CD_isActive" = 1;
    "CD_subscriptionType" = free;
    "CD_updatedAt" = "2025-08-03 16:07:52 +0000";
    "CD_user" = "8E410595-8274-4BE2-82A1-C901F797F42C";
}>
Modified Fields: (
    "CD_endDate",
    "CD_updatedAt",
    "CD_productIdentifier_ckAsset",
    "CD_isActive",
    "CD_productIdentifier",
    "CD_subscriptionType_ckAsset",
    "CD_subscriptionType",
    "CD_user",
    "CD_startDate",
    "CD_createdAt",
    "CD_id",
    "CD_entityName"
)
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExportContext newOperationBySerializingDirtyObjectsInStore:inManagedObjectContext:error:]_block_invoke(761): Serializer generated record for object:
<Subscription: 0x300c704b0> (entity: Subscription; id: 0x95726ce5d347b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p1>; data: {
    createdAt = "2025-08-03 16:07:52 +0000";
    endDate = nil;
    id = "6A45A1E9-19DE-42A0-BD65-D68217624D2E";
    isActive = 1;
    productIdentifier = nil;
    startDate = nil;
    subscriptionType = free;
    updatedAt = "2025-08-03 16:07:52 +0000";
    user = "0x95726ce5d347b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p1>";
})
(
    "<CKRecord: 0x10de69190; recordType=CD_Subscription, recordID=9EDF0DE8-A847-48E9-BA6E-82F18EA8F48F:(com.apple.coredata.cloudkit.zone:__defaultOwner__), values={\n    \"CD_createdAt\" = \"2025-08-03 16:07:52 +0000\";\n    \"CD_entityName\" = Subscription;\n    \"CD_id\" = \"6A45A1E9-19DE-42A0-BD65-D68217624D2E\";\n    \"CD_isActive\" = 1;\n    \"CD_subscriptionType\" = free;\n    \"CD_updatedAt\" = \"2025-08-03 16:07:52 +0000\";\n    \"CD_user\" = \"8E410595-8274-4BE2-82A1-C901F797F42C\";\n}>"
)
(
        (
        "CD_endDate",
        "CD_updatedAt",
        "CD_productIdentifier_ckAsset",
        "CD_isActive",
        "CD_productIdentifier",
        "CD_subscriptionType_ckAsset",
        "CD_subscriptionType",
        "CD_user",
        "CD_startDate",
        "CD_createdAt",
        "CD_id",
        "CD_entityName"
    )
)
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer newCKRecordsFromObject:fullyMaterializeRecords:includeRelationships:error:](576): Serializer has finished creating record: <CKRecord: 0x10de045b0; recordType=CD_User, recordID=8E410595-8274-4BE2-82A1-C901F797F42C:(com.apple.coredata.cloudkit.zone:__defaultOwner__), values={
    "CD_appleUserID" = "001426.59411a4866d346eb8f0b45fe544652d9.1600";
    "CD_createdAt" = "2025-08-03 16:07:52 +0000";
    "CD_entityName" = User;
    "CD_id" = "E0296998-6D4D-4C8E-9775-A84908D11CAF";
    "CD_nickname" = "\U5bb6\U957f";
    "CD_subscription" = "9EDF0DE8-A847-48E9-BA6E-82F18EA8F48F";
}>
Modified Fields: (
    "CD_nickname",
    "CD_appleUserID_ckAsset",
    "CD_subscription",
    "CD_appleUserID",
    "CD_email_ckAsset",
    "CD_email",
    "CD_id",
    "CD_nickname_ckAsset",
    "CD_createdAt",
    "CD_entityName"
)
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExportContext newOperationBySerializingDirtyObjectsInStore:inManagedObjectContext:error:]_block_invoke(761): Serializer generated record for object:
<User: 0x300c73f20> (entity: User; id: 0x95726ce5d347b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p1>; data: {
    appleUserID = "001426.59411a4866d346eb8f0b45fe544652d9.1600";
    createdAt = "2025-08-03 16:07:52 +0000";
    email = nil;
    globalRules = "<relationship fault: 0x302f1a660 'globalRules'>";
    id = "E0296998-6D4D-4C8E-9775-A84908D11CAF";
    members = "<relationship fault: 0x302f18ce0 'members'>";
    nickname = "\U5bb6\U957f";
    subscription = "0x95726ce5d347b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p1>";
})
(
    "<CKRecord: 0x10de045b0; recordType=CD_User, recordID=8E410595-8274-4BE2-82A1-C901F797F42C:(com.apple.coredata.cloudkit.zone:__defaultOwner__), values={\n    \"CD_appleUserID\" = \"001426.59411a4866d346eb8f0b45fe544652d9.1600\";\n    \"CD_createdAt\" = \"2025-08-03 16:07:52 +0000\";\n    \"CD_entityName\" = User;\n    \"CD_id\" = \"E0296998-6D4D-4C8E-9775-A84908D11CAF\";\n    \"CD_nickname\" = \"\\U5bb6\\U957f\";\n    \"CD_subscription\" = \"9EDF0DE8-A847-48E9-BA6E-82F18EA8F48F\";\n}>"
)
(
        (
        "CD_nickname",
        "CD_appleUserID_ckAsset",
        "CD_subscription",
        "CD_appleUserID",
        "CD_email_ckAsset",
        "CD_email",
        "CD_id",
        "CD_nickname_ckAsset",
        "CD_createdAt",
        "CD_entityName"
    )
)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:](3659): <NSCloudKitMirroringDelegate: 0x301060000> - Beginning automated export - ExportActivity:
<CKSchedulerActivity: 0x300b628e0; identifier=com.apple.coredata.cloudkit.activity.export.4F40BB33-799D-403B-8C2B-16613187D9A1, priority=2, container=iCloud.com.rainkygong.ztt2:Sandbox, relatedApplications=(
    "com.rainkygong.ztt2"
), xpcActivityCriteriaOverrides={
    ActivityGroupName = "com.apple.coredata.cloudkit.ztt2.4F40BB33-799D-403B-8C2B-16613187D9A1";
    Delay = 0;
    Priority = Utility;
}>
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate executeMirroringRequest:error:](976): <NSCloudKitMirroringDelegate: 0x301060000>: Asked to execute request: <NSCloudKitMirroringExportRequest: 0x300c4b4d0> BF6D90EF-6445-42E8-9C78-94C3E3FEB565
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke(1010): <NSCloudKitMirroringDelegate: 0x301060000>: enqueuing request: <NSCloudKitMirroringExportRequest: 0x300c4b4d0> BF6D90EF-6445-42E8-9C78-94C3E3FEB565
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke_2(1019): Enqueued request: <NSCloudKitMirroringExportRequest: 0x300c4b4d0> BF6D90EF-6445-42E8-9C78-94C3E3FEB565
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3544): <NSCloudKitMirroringDelegate: 0x301060000>: Deferring additional work. There is still an active request: <NSCloudKitMirroringExportRequest: 0x300c4a800> CE352D4A-BF70-477F-A7E2-8DD73C07FED5
CoreData: warning: CoreData+CloudKit: -[PFCloudKitExporter exportOperationFinished:withSavedRecords:deletedRecordIDs:operationError:](670): Modify records finished: (
    "<CKRecord: 0x10de69190; recordType=CD_Subscription, recordID=9EDF0DE8-A847-48E9-BA6E-82F18EA8F48F:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=26, values={\n    \"CD_createdAt\" = \"2025-08-03 16:07:52 +0000\";\n    \"CD_entityName\" = Subscription;\n    \"CD_id\" = \"6A45A1E9-19DE-42A0-BD65-D68217624D2E\";\n    \"CD_isActive\" = 1;\n    \"CD_subscriptionType\" = free;\n    \"CD_updatedAt\" = \"2025-08-03 16:07:52 +0000\";\n    \"CD_user\" = \"8E410595-8274-4BE2-82A1-C901F797F42C\";\n}>",
    "<CKRecord: 0x10de045b0; recordType=CD_User, recordID=8E410595-8274-4BE2-82A1-C901F797F42C:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=27, values={\n    \"CD_appleUserID\" = \"001426.59411a4866d346eb8f0b45fe544652d9.1600\";\n    \"CD_createdAt\" = \"2025-08-03 16:07:52 +0000\";\n    \"CD_entityName\" = User;\n    \"CD_id\" = \"E0296998-6D4D-4C8E-9775-A84908D11CAF\";\n    \"CD_nickname\" = \"\\U5bb6\\U957f\";\n    \"CD_subscription\" = \"9EDF0DE8-A847-48E9-BA6E-82F18EA8F48F\";\n}>"
)
(
)
(null)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301460f70>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301460f70>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter analyzeHistoryInStore:withManagedObjectContext:error:](531): <PFCloudKitExporter: 0x300c3a080>: Exporting changes since (0): <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301479110>
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter exportIfNecessary]_block_invoke_2(345): <PFCloudKitExporter: 0x300c3a080>: Found 0 objects needing export.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30147e220>
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _exportFinishedWithResult:exporter:](1482): Finished export: <PFCloudKitExporter: 0x300c3a080>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _finishedRequest:withResult:](3566): Finished request: <NSCloudKitMirroringExportRequest: 0x300c4a800> CE352D4A-BF70-477F-A7E2-8DD73C07FED5 with result: <NSCloudKitMirroringResult: 0x3021d0210> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 1 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:]_block_invoke(3673): <NSCloudKitMirroringDelegate: 0x301060000> - Finished automatic export - SandboxExport - with result: <NSCloudKitMirroringResult: 0x3021d0210> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 1 error: (null)
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate finishedAutomatedRequestWithResult:](3579): Finished request '<NSCloudKitMirroringExportRequest: 0x300c4a800> CE352D4A-BF70-477F-A7E2-8DD73C07FED5' with result: <NSCloudKitMirroringResult: 0x3021d0210> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 1 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3548): <NSCloudKitMirroringDelegate: 0x301060000>: Executing: <NSCloudKitMirroringExportRequest: 0x300c4b4d0> BF6D90EF-6445-42E8-9C78-94C3E3FEB565
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301460f70>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter analyzeHistoryInStore:withManagedObjectContext:error:](531): <PFCloudKitExporter: 0x300c47c00>: Exporting changes since (0): <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30144c5b0>
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter exportIfNecessary]_block_invoke_2(345): <PFCloudKitExporter: 0x300c47c00>: Found 0 objects needing export.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30146ca90>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _exportFinishedWithResult:exporter:](1482): Finished export: <PFCloudKitExporter: 0x300c47c00>
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _finishedRequest:withResult:](3566): Finished request: <NSCloudKitMirroringExportRequest: 0x300c4b4d0> BF6D90EF-6445-42E8-9C78-94C3E3FEB565 with result: <NSCloudKitMirroringResult: 0x3021fb690> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:]_block_invoke(3673): <NSCloudKitMirroringDelegate: 0x301060000> - Finished automatic export - ExportActivity - with result: <NSCloudKitMirroringResult: 0x3021fb690> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate finishedAutomatedRequestWithResult:](3579): Finished request '<NSCloudKitMirroringExportRequest: 0x300c4b4d0> BF6D90EF-6445-42E8-9C78-94C3E3FEB565' with result: <NSCloudKitMirroringResult: 0x3021fb690> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3551): <NSCloudKitMirroringDelegate: 0x301060000>: No more requests to execute.
加载用户数据：家长
✅ CloudKit account is available
🔄 开始初始化刮刮卡配置数据...
📝 没有选中成员，使用默认值
加载用户数据：家长
CoreData: debug: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:](49): <PFCloudKitThrottledNotificationObserver: 0x3021ff840>: Got: UISceneWillDeactivateNotification - 0
CoreData: debug: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:](49): <PFCloudKitThrottledNotificationObserver: 0x3021ff840>: Got: UIApplicationWillResignActiveNotification - 1
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:](70): <PFCloudKitThrottledNotificationObserver: 0x3021ff840> - Already scheduled a block to respond to 'UIApplicationWillResignActiveNotification', 2 notifications since.
CoreData: debug: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:]_block_invoke(57): <PFCloudKitThrottledNotificationObserver: 0x3021ff840>: Executing 'AppDeactivateObserver' block for 'UISceneWillDeactivateNotification' clearing 2 iterations.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:](3659): <NSCloudKitMirroringDelegate: 0x301060000> - Beginning automated export - AppDeactivationExport:
(null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate executeMirroringRequest:error:](976): <NSCloudKitMirroringDelegate: 0x301060000>: Asked to execute request: <NSCloudKitMirroringExportRequest: 0x300cd84b0> 5190C014-2E79-457B-AD99-727F8DE650B1
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke(1010): <NSCloudKitMirroringDelegate: 0x301060000>: enqueuing request: <NSCloudKitMirroringExportRequest: 0x300cd84b0> 5190C014-2E79-457B-AD99-727F8DE650B1
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke_2(1019): Enqueued request: <NSCloudKitMirroringExportRequest: 0x300cd84b0> 5190C014-2E79-457B-AD99-727F8DE650B1
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3548): <NSCloudKitMirroringDelegate: 0x301060000>: Executing: <NSCloudKitMirroringExportRequest: 0x300cd84b0> 5190C014-2E79-457B-AD99-727F8DE650B1
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301472be0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
📡 检测到远程数据变化
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter analyzeHistoryInStore:withManagedObjectContext:error:](531): <PFCloudKitExporter: 0x300cd85a0>: Exporting changes since (0): <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301446970>
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter exportIfNecessary]_block_invoke_2(345): <PFCloudKitExporter: 0x300cd85a0>: Found 0 objects needing export.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x3014715f0>
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _exportFinishedWithResult:exporter:](1482): Finished export: <PFCloudKitExporter: 0x300cd85a0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _finishedRequest:withResult:](3566): Finished request: <NSCloudKitMirroringExportRequest: 0x300cd84b0> 5190C014-2E79-457B-AD99-727F8DE650B1 with result: <NSCloudKitMirroringResult: 0x3021bb360> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:]_block_invoke(3673): <NSCloudKitMirroringDelegate: 0x301060000> - Finished automatic export - AppDeactivationExport - with result: <NSCloudKitMirroringResult: 0x3021bb360> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
📡 检测到远程数据变化
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate finishedAutomatedRequestWithResult:](3579): Finished request '<NSCloudKitMirroringExportRequest: 0x300cd84b0> 5190C014-2E79-457B-AD99-727F8DE650B1' with result: <NSCloudKitMirroringResult: 0x3021bb360> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3551): <NSCloudKitMirroringDelegate: 0x301060000>: No more requests to execute.
CoreData: debug: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:](49): <PFCloudKitThrottledNotificationObserver: 0x3021feb20>: Got: UISceneDidActivateNotification - 0
CoreData: debug: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:](49): <PFCloudKitThrottledNotificationObserver: 0x3021feb20>: Got: UIApplicationDidBecomeActiveNotification - 1
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:](70): <PFCloudKitThrottledNotificationObserver: 0x3021feb20> - Already scheduled a block to respond to 'UIApplicationDidBecomeActiveNotification', 2 notifications since.
加载用户数据：家长
CoreData: debug: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:]_block_invoke(57): <PFCloudKitThrottledNotificationObserver: 0x3021feb20>: Executing 'AppActivateObserver' block for 'UISceneDidActivateNotification' clearing 2 iterations.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:](3659): <NSCloudKitMirroringDelegate: 0x301060000> - Beginning automated export - AppActivationExport:
(null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate executeMirroringRequest:error:](976): <NSCloudKitMirroringDelegate: 0x301060000>: Asked to execute request: <NSCloudKitMirroringExportRequest: 0x300c17480> 6447704D-018E-4AEA-A687-B327686ADC66
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedImportWithLabel:activity:voucher:completionHandler:](3620): <NSCloudKitMirroringDelegate: 0x301060000> - Beginning automated import - AppActivationImport - in response to activity:
(null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate executeMirroringRequest:error:](976): <NSCloudKitMirroringDelegate: 0x301060000>: Asked to execute request: <NSCloudKitMirroringImportRequest: 0x300c154f0> 9051CB42-6E15-4F52-8937-68FE404E4C10
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke(1010): <NSCloudKitMirroringDelegate: 0x301060000>: enqueuing request: <NSCloudKitMirroringExportRequest: 0x300c17480> 6447704D-018E-4AEA-A687-B327686ADC66
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke_2(1019): Enqueued request: <NSCloudKitMirroringExportRequest: 0x300c17480> 6447704D-018E-4AEA-A687-B327686ADC66
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke(1010): <NSCloudKitMirroringDelegate: 0x301060000>: enqueuing request: <NSCloudKitMirroringImportRequest: 0x300c154f0> 9051CB42-6E15-4F52-8937-68FE404E4C10
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke_2(1019): Enqueued request: <NSCloudKitMirroringImportRequest: 0x300c154f0> 9051CB42-6E15-4F52-8937-68FE404E4C10
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3548): <NSCloudKitMirroringDelegate: 0x301060000>: Executing: <NSCloudKitMirroringImportRequest: 0x300c154f0> 9051CB42-6E15-4F52-8937-68FE404E4C10
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3544): <NSCloudKitMirroringDelegate: 0x301060000>: Deferring additional work. There is still an active request: <NSCloudKitMirroringImportRequest: 0x300c154f0> 9051CB42-6E15-4F52-8937-68FE404E4C10
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301460ea0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
📡 检测到远程数据变化
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitImporter databaseFetchFinishWithContext:error:completion:]_block_invoke(304): <PFCloudKitImporter: 0x303a1a7c0>: Import request finished: <NSCloudKitMirroringImportRequest: 0x300c154f0> 9051CB42-6E15-4F52-8937-68FE404E4C10 - <PFCloudKitImportDatabaseContext: 0x3021d4e70> {
Token: <CKServerChangeToken: 0x302d44210; data=AQAAAZhwsJkHB9x90HCEEfC1zQMuSMWzVw==>
Changed:
{(
    <CKRecordZoneID: 0x3021d4c90; zoneName=com.apple.coredata.cloudkit.zone, ownerName=__defaultOwner__>
)}
}
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitImporter processWorkItemsWithCompletion:](418): <PFCloudKitImporter: 0x303a1a7c0>: Processing work items: (
    "<PFCloudKitImporterZoneChangedWorkItem: 0x3018658c0 - <NSCloudKitMirroringImportRequest: 0x300c154f0> 9051CB42-6E15-4F52-8937-68FE404E4C10> {\n(\n    \"<CKRecordZoneID: 0x3021d4c90; zoneName=com.apple.coredata.cloudkit.zone, ownerName=__defaultOwner__>\"\n)\n}"
)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30147de10>
📡 检测到远程数据变化
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:](3659): <NSCloudKitMirroringDelegate: 0x301060000> - Beginning automated export - ExportActivity:
<CKSchedulerActivity: 0x300b5b9c0; identifier=com.apple.coredata.cloudkit.activity.export.4F40BB33-799D-403B-8C2B-16613187D9A1, priority=2, container=iCloud.com.rainkygong.ztt2:Sandbox, relatedApplications=(
    "com.rainkygong.ztt2"
), xpcActivityCriteriaOverrides={
    ActivityGroupName = "com.apple.coredata.cloudkit.ztt2.4F40BB33-799D-403B-8C2B-16613187D9A1";
    Delay = 0;
    Priority = Utility;
}>
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate executeMirroringRequest:error:](976): <NSCloudKitMirroringDelegate: 0x301060000>: Asked to execute request: <NSCloudKitMirroringExportRequest: 0x300c14dc0> DEF08CF7-D96D-4C61-956E-627EC5256C34
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke(1010): <NSCloudKitMirroringDelegate: 0x301060000>: enqueuing request: <NSCloudKitMirroringExportRequest: 0x300c14dc0> DEF08CF7-D96D-4C61-956E-627EC5256C34
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke_2(1022): Failed to enqueue request: <NSCloudKitMirroringExportRequest: 0x300c14dc0> DEF08CF7-D96D-4C61-956E-627EC5256C34
Error Domain=NSCocoaErrorDomain Code=134417 "Request '<NSCloudKitMirroringExportRequest: 0x300c14dc0> DEF08CF7-D96D-4C61-956E-627EC5256C34' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'." UserInfo={NSLocalizedFailureReason=Request '<NSCloudKitMirroringExportRequest: 0x300c14dc0> DEF08CF7-D96D-4C61-956E-627EC5256C34' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'.}
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:]_block_invoke(3673): <NSCloudKitMirroringDelegate: 0x301060000> - Finished automatic export - ExportActivity - with result: <NSCloudKitMirroringResult: 0x3021fec40> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 0 madeChanges: 0 error: Error Domain=NSCocoaErrorDomain Code=134417 "Request '<NSCloudKitMirroringExportRequest: 0x300c14dc0> DEF08CF7-D96D-4C61-956E-627EC5256C34' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'." UserInfo={NSLocalizedFailureReason=Request '<NSCloudKitMirroringExportRequest: 0x300c14dc0> DEF08CF7-D96D-4C61-956E-627EC5256C34' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'.}
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate finishedAutomatedRequestWithResult:](3579): Finished request '<NSCloudKitMirroringExportRequest: 0x300c14dc0> DEF08CF7-D96D-4C61-956E-627EC5256C34' with result: <NSCloudKitMirroringResult: 0x3021fec40> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 0 madeChanges: 0 error: Error Domain=NSCocoaErrorDomain Code=134417 "Request '<NSCloudKitMirroringExportRequest: 0x300c14dc0> DEF08CF7-D96D-4C61-956E-627EC5256C34' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'." UserInfo={NSLocalizedFailureReason=Request '<NSCloudKitMirroringExportRequest: 0x300c14dc0> DEF08CF7-D96D-4C61-956E-627EC5256C34' was cancelled because there is already a pending request of type 'NSCloudKitMirroringExportRequest'.}
🔄 开始初始化刮刮卡配置数据...
📝 没有选中成员，使用默认值
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportRecordsWorkItem applyAccumulatedChangesToStore:inManagedObjectContext:withStoreMonitor:madeChanges:error:]_block_invoke(377): <PFCloudKitImporterZoneChangedWorkItem: 0x3018658c0 - <NSCloudKitMirroringImportRequest: 0x300c154f0> 9051CB42-6E15-4F52-8937-68FE404E4C10> {
(
    "<CKRecordZoneID: 0x3021d4c90; zoneName=com.apple.coredata.cloudkit.zone, ownerName=__defaultOwner__>"
)
} - Importing updated records:
(
    "<CKRecord: 0x10f10f230; recordType=CD_Subscription, recordID=9EDF0DE8-A847-48E9-BA6E-82F18EA8F48F:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=26, values={\n    \"CD_createdAt\" = \"2025-08-03 16:07:52 +0000\";\n    \"CD_entityName\" = Subscription;\n    \"CD_id\" = \"6A45A1E9-19DE-42A0-BD65-D68217624D2E\";\n    \"CD_isActive\" = 1;\n    \"CD_subscriptionType\" = free;\n    \"CD_updatedAt\" = \"2025-08-03 16:07:52 +0000\";\n    \"CD_user\" = \"8E410595-8274-4BE2-82A1-C901F797F42C\";\n}>",
    "<CKRecord: 0x10f1192a0; recordType=CD_User, recordID=8E410595-8274-4BE2-82A1-C901F797F42C:(com.apple.coredata.cloudkit.zone:__defaultOwner__), recordChangeTag=27, values={\n    \"CD_appleUserID\" = \"001426.59411a4866d346eb8f0b45fe544652d9.1600\";\n    \"CD_createdAt\" = \"2025-08-03 16:07:52 +0000\";\n    \"CD_entityName\" = User;\n    \"CD_id\" = \"E0296998-6D4D-4C8E-9775-A84908D11CAF\";\n    \"CD_nickname\" = \"\\U5bb6\\U957f\";\n    \"CD_subscription\" = \"9EDF0DE8-A847-48E9-BA6E-82F18EA8F48F\";\n}>"
)
Deleted RecordIDs:
{
}
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f31560; recordName=9EDF0DE8-A847-48E9-BA6E-82F18EA8F48F, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 8E410595-8274-4BE2-82A1-C901F797F42C by user
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer updateAttributes:andRelationships:onManagedObject:fromRecord:withRecordMetadata:importContext:error:](1496): Adding mirrored relationship to link for record <CKRecordID: 0x302f23a40; recordName=8E410595-8274-4BE2-82A1-C901F797F42C, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> related to 9EDF0DE8-A847-48E9-BA6E-82F18EA8F48F by subscription
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464820>
CoreData: debug: CoreData+CloudKit: -[PFCloudKitImportZoneContext populateUnresolvedIDsInStore:withManagedObjectContext:error:]_block_invoke(551): Populating unresolved relationships:
{
}
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x3021c11a0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f31560; recordName=9EDF0DE8-A847-48E9-BA6E-82F18EA8F48F, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f31920; recordName=8E410595-8274-4BE2-82A1-C901F797F42C, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via user on 0x95726ce5d347b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p1>->0x95726ce5d347b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p1>
📡 检测到远程数据变化
CoreData: debug: CoreData+CloudKit: -[PFCloudKitSerializer applyUpdatedRecords:deletedRecordIDs:toStore:inManagedObjectContext:onlyUpdatingAttributes:andRelationships:madeChanges:error:]_block_invoke_3(887): Updating relationship: <PFMirroredOneToManyRelationship: 0x302116bb0>
CoreData: debug: CoreData+CloudKit: -[PFMirroredOneToManyRelationship updateRelationshipValueUsingImportContext:andManagedObjectContext:error:](441): Linking object with record name <CKRecordID: 0x302f23a40; recordName=8E410595-8274-4BE2-82A1-C901F797F42C, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> to <CKRecordID: 0x302f208a0; recordName=9EDF0DE8-A847-48E9-BA6E-82F18EA8F48F, zoneID=com.apple.coredata.cloudkit.zone:__defaultOwner__> via subscription on 0x95726ce5d347b8da <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/User/p1>->0x95726ce5d347b8ca <x-coredata://4F40BB33-799D-403B-8C2B-16613187D9A1/Subscription/p1>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464820>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
📡 检测到远程数据变化
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464820>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
📡 检测到远程数据变化
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464820>
CoreData: warning: CoreData+CloudKit: -[PFCloudKitImportRecordsWorkItem applyAccumulatedChangesToStore:inManagedObjectContext:withStoreMonitor:madeChanges:error:](513): Finished importing applying changes for request: <NSCloudKitMirroringImportRequest: 0x300c154f0> 9051CB42-6E15-4F52-8937-68FE404E4C10
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464820>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301464820>
CoreData: warning: CoreData+CloudKit: -[PFCloudKitImportRecordsWorkItem newMirroringResultByApplyingAccumulatedChanges]_block_invoke(241): Finished importing changes for request: <NSCloudKitMirroringImportRequest: 0x300c154f0> 9051CB42-6E15-4F52-8937-68FE404E4C10
📡 检测到远程数据变化
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitImporter processWorkItemsWithCompletion:](418): <PFCloudKitImporter: 0x303a1a7c0>: Processing work items: (
)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30144c8f0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
📡 检测到远程数据变化
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x3014449c0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
📡 检测到远程数据变化
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _finishedRequest:withResult:](3566): Finished request: <NSCloudKitMirroringImportRequest: 0x300c154f0> 9051CB42-6E15-4F52-8937-68FE404E4C10 with result: <NSCloudKitMirroringResult: 0x3021a6550> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 1 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedImportWithLabel:activity:voucher:completionHandler:]_block_invoke(3633): <NSCloudKitMirroringDelegate: 0x301060000> - Finished automatic import - AppActivationImport - with result: <NSCloudKitMirroringResult: 0x3021a6550> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 1 error: (null)
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate finishedAutomatedRequestWithResult:](3579): Finished request '<NSCloudKitMirroringImportRequest: 0x300c154f0> 9051CB42-6E15-4F52-8937-68FE404E4C10' with result: <NSCloudKitMirroringResult: 0x3021a6550> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 1 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndScheduleImportIfNecessaryFromPush:fromErrorRecovery:fromShareAccept:andStartAfterDate:]_block_invoke_2(3314): <NSCloudKitMirroringDelegate: 0x301060000>: Checking to see if an automated import should be scheduled.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3548): <NSCloudKitMirroringDelegate: 0x301060000>: Executing: <NSCloudKitMirroringExportRequest: 0x300c17480> 6447704D-018E-4AEA-A687-B327686ADC66
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x3014449c0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
📡 检测到远程数据变化
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter analyzeHistoryInStore:withManagedObjectContext:error:](531): <PFCloudKitExporter: 0x300ce2e40>: Exporting changes since (0): <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x3014449c0>
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter exportIfNecessary]_block_invoke_2(345): <PFCloudKitExporter: 0x300ce2e40>: Found 0 objects needing export.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30146abe0>
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _exportFinishedWithResult:exporter:](1482): Finished export: <PFCloudKitExporter: 0x300ce2e40>
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _finishedRequest:withResult:](3566): Finished request: <NSCloudKitMirroringExportRequest: 0x300c17480> 6447704D-018E-4AEA-A687-B327686ADC66 with result: <NSCloudKitMirroringResult: 0x3021ea550> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:]_block_invoke(3673): <NSCloudKitMirroringDelegate: 0x301060000> - Finished automatic export - AppActivationExport - with result: <NSCloudKitMirroringResult: 0x3021ea550> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate finishedAutomatedRequestWithResult:](3579): Finished request '<NSCloudKitMirroringExportRequest: 0x300c17480> 6447704D-018E-4AEA-A687-B327686ADC66' with result: <NSCloudKitMirroringResult: 0x3021ea550> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
📡 检测到远程数据变化
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3551): <NSCloudKitMirroringDelegate: 0x301060000>: No more requests to execute.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:](3659): <NSCloudKitMirroringDelegate: 0x301060000> - Beginning automated export - ExportActivity:
<CKSchedulerActivity: 0x300b47de0; identifier=com.apple.coredata.cloudkit.activity.export.4F40BB33-799D-403B-8C2B-16613187D9A1, priority=2, container=iCloud.com.rainkygong.ztt2:Sandbox, relatedApplications=(
    "com.rainkygong.ztt2"
), xpcActivityCriteriaOverrides={
    ActivityGroupName = "com.apple.coredata.cloudkit.ztt2.4F40BB33-799D-403B-8C2B-16613187D9A1";
    Delay = 0;
    Priority = Utility;
}>
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate executeMirroringRequest:error:](976): <NSCloudKitMirroringDelegate: 0x301060000>: Asked to execute request: <NSCloudKitMirroringExportRequest: 0x300c1a940> 7E6757B2-DBB5-44C0-B35F-27348BBC7D92
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke(1010): <NSCloudKitMirroringDelegate: 0x301060000>: enqueuing request: <NSCloudKitMirroringExportRequest: 0x300c1a940> 7E6757B2-DBB5-44C0-B35F-27348BBC7D92
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke_2(1019): Enqueued request: <NSCloudKitMirroringExportRequest: 0x300c1a940> 7E6757B2-DBB5-44C0-B35F-27348BBC7D92
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3548): <NSCloudKitMirroringDelegate: 0x301060000>: Executing: <NSCloudKitMirroringExportRequest: 0x300c1a940> 7E6757B2-DBB5-44C0-B35F-27348BBC7D92
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30147dfb0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
📡 检测到远程数据变化
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter analyzeHistoryInStore:withManagedObjectContext:error:](531): <PFCloudKitExporter: 0x300ce2670>: Exporting changes since (0): <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301448d00>
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter exportIfNecessary]_block_invoke_2(345): <PFCloudKitExporter: 0x300ce2670>: Found 0 objects needing export.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30146abe0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _exportFinishedWithResult:exporter:](1482): Finished export: <PFCloudKitExporter: 0x300ce2670>
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _finishedRequest:withResult:](3566): Finished request: <NSCloudKitMirroringExportRequest: 0x300c1a940> 7E6757B2-DBB5-44C0-B35F-27348BBC7D92 with result: <NSCloudKitMirroringResult: 0x3021ea130> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:]_block_invoke(3673): <NSCloudKitMirroringDelegate: 0x301060000> - Finished automatic export - ExportActivity - with result: <NSCloudKitMirroringResult: 0x3021ea130> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
📡 检测到远程数据变化
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate finishedAutomatedRequestWithResult:](3579): Finished request '<NSCloudKitMirroringExportRequest: 0x300c1a940> 7E6757B2-DBB5-44C0-B35F-27348BBC7D92' with result: <NSCloudKitMirroringResult: 0x3021ea130> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3551): <NSCloudKitMirroringDelegate: 0x301060000>: No more requests to execute.
🔄 开始初始化刮刮卡配置数据...
📝 没有选中成员，使用默认值
加载用户数据：家长
🔄 开始初始化刮刮卡配置数据...
📝 没有选中成员，使用默认值
加载用户数据：家长
开关状态改变: iCloud同步 -> false
🔄 开始初始化刮刮卡配置数据...
📝 没有选中成员，使用默认值
加载用户数据：家长
🔄 开始初始化刮刮卡配置数据...
📝 没有选中成员，使用默认值
加载用户数据：家长
🔄 开始初始化刮刮卡配置数据...
📝 没有选中成员，使用默认值
CoreData: debug: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:](49): <PFCloudKitThrottledNotificationObserver: 0x3021ff840>: Got: UISceneWillDeactivateNotification - 0
CoreData: debug: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:](49): <PFCloudKitThrottledNotificationObserver: 0x3021ff840>: Got: UIApplicationWillResignActiveNotification - 1
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:](70): <PFCloudKitThrottledNotificationObserver: 0x3021ff840> - Already scheduled a block to respond to 'UIApplicationWillResignActiveNotification', 2 notifications since.
CoreData: debug: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:](49): <PFCloudKitThrottledNotificationObserver: 0x3021ff840>: Got: UISceneDidEnterBackgroundNotification - 2
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:](70): <PFCloudKitThrottledNotificationObserver: 0x3021ff840> - Already scheduled a block to respond to 'UISceneDidEnterBackgroundNotification', 3 notifications since.
CoreData: debug: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:]_block_invoke(57): <PFCloudKitThrottledNotificationObserver: 0x3021ff840>: Executing 'AppDeactivateObserver' block for 'UISceneWillDeactivateNotification' clearing 3 iterations.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:](3659): <NSCloudKitMirroringDelegate: 0x301060000> - Beginning automated export - AppDeactivationExport:
(null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate executeMirroringRequest:error:](976): <NSCloudKitMirroringDelegate: 0x301060000>: Asked to execute request: <NSCloudKitMirroringExportRequest: 0x300ce7200> 75FCE90A-F4A1-4016-9668-F8302BAD1B40
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke(1010): <NSCloudKitMirroringDelegate: 0x301060000>: enqueuing request: <NSCloudKitMirroringExportRequest: 0x300ce7200> 75FCE90A-F4A1-4016-9668-F8302BAD1B40
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke_2(1019): Enqueued request: <NSCloudKitMirroringExportRequest: 0x300ce7200> 75FCE90A-F4A1-4016-9668-F8302BAD1B40
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3548): <NSCloudKitMirroringDelegate: 0x301060000>: Executing: <NSCloudKitMirroringExportRequest: 0x300ce7200> 75FCE90A-F4A1-4016-9668-F8302BAD1B40
CoreData: debug: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:](49): <PFCloudKitThrottledNotificationObserver: 0x3021ff840>: Got: UIApplicationDidEnterBackgroundNotification - 0
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30147a080>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter analyzeHistoryInStore:withManagedObjectContext:error:](531): <PFCloudKitExporter: 0x300cd0eb0>: Exporting changes since (0): <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x3014684e0>
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter exportIfNecessary]_block_invoke_2(345): <PFCloudKitExporter: 0x300cd0eb0>: Found 0 objects needing export.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301468410>
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _exportFinishedWithResult:exporter:](1482): Finished export: <PFCloudKitExporter: 0x300cd0eb0>
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _finishedRequest:withResult:](3566): Finished request: <NSCloudKitMirroringExportRequest: 0x300ce7200> 75FCE90A-F4A1-4016-9668-F8302BAD1B40 with result: <NSCloudKitMirroringResult: 0x3021ea460> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:]_block_invoke(3673): <NSCloudKitMirroringDelegate: 0x301060000> - Finished automatic export - AppDeactivationExport - with result: <NSCloudKitMirroringResult: 0x3021ea460> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate finishedAutomatedRequestWithResult:](3579): Finished request '<NSCloudKitMirroringExportRequest: 0x300ce7200> 75FCE90A-F4A1-4016-9668-F8302BAD1B40' with result: <NSCloudKitMirroringResult: 0x3021ea460> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3551): <NSCloudKitMirroringDelegate: 0x301060000>: No more requests to execute.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
📡 检测到远程数据变化
📡 检测到远程数据变化
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[PFCloudKitThrottledNotificationObserver noteRecievedNotification:]_block_invoke(57): <PFCloudKitThrottledNotificationObserver: 0x3021ff840>: Executing 'AppDeactivateObserver' block for 'UIApplicationDidEnterBackgroundNotification' clearing 1 iterations.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:](3659): <NSCloudKitMirroringDelegate: 0x301060000> - Beginning automated export - AppDeactivationExport:
(null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate executeMirroringRequest:error:](976): <NSCloudKitMirroringDelegate: 0x301060000>: Asked to execute request: <NSCloudKitMirroringExportRequest: 0x300c18190> 2FF18EDF-27A6-43C9-A31E-EFB8937C70BB
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke(1010): <NSCloudKitMirroringDelegate: 0x301060000>: enqueuing request: <NSCloudKitMirroringExportRequest: 0x300c18190> 2FF18EDF-27A6-43C9-A31E-EFB8937C70BB
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _enqueueRequest:]_block_invoke_2(1019): Enqueued request: <NSCloudKitMirroringExportRequest: 0x300c18190> 2FF18EDF-27A6-43C9-A31E-EFB8937C70BB
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3548): <NSCloudKitMirroringDelegate: 0x301060000>: Executing: <NSCloudKitMirroringExportRequest: 0x300c18190> 2FF18EDF-27A6-43C9-A31E-EFB8937C70BB
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x30147d1e0>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
📡 检测到远程数据变化
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter analyzeHistoryInStore:withManagedObjectContext:error:](531): <PFCloudKitExporter: 0x300c4eee0>: Exporting changes since (0): <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x3014630c0>
CoreData: CloudKit: CoreData+CloudKit: -[PFCloudKitExporter exportIfNecessary]_block_invoke_2(345): <PFCloudKitExporter: 0x300c4eee0>: Found 0 objects needing export.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate managedObjectContextSaved:](3113): <NSCloudKitMirroringDelegate: 0x301060000>: Observed context save: <NSPersistentStoreCoordinator: 0x30047c690> - <NSManagedObjectContext: 0x301478f70>
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:](3156): <NSCloudKitMirroringDelegate: 0x301060000>: Observed remote store notification: <NSPersistentStoreCoordinator: 0x30047c690> - 4F40BB33-799D-403B-8C2B-16613187D9A1 - <NSPersistentHistoryToken - {
    "4F40BB33-799D-403B-8C2B-16613187D9A1" = 3;
}> - file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _exportFinishedWithResult:exporter:](1482): Finished export: <PFCloudKitExporter: 0x300c4eee0>
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _finishedRequest:withResult:](3566): Finished request: <NSCloudKitMirroringExportRequest: 0x300c18190> 2FF18EDF-27A6-43C9-A31E-EFB8937C70BB with result: <NSCloudKitMirroringResult: 0x3021d7e70> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
📡 检测到远程数据变化
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate _scheduleAutomatedExportWithLabel:activity:voucher:completionHandler:]_block_invoke(3673): <NSCloudKitMirroringDelegate: 0x301060000> - Finished automatic export - AppDeactivationExport - with result: <NSCloudKitMirroringResult: 0x3021d7e70> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: warning: CoreData+CloudKit: -[NSCloudKitMirroringDelegate finishedAutomatedRequestWithResult:](3579): Finished request '<NSCloudKitMirroringExportRequest: 0x300c18190> 2FF18EDF-27A6-43C9-A31E-EFB8937C70BB' with result: <NSCloudKitMirroringResult: 0x3021d7e70> storeIdentifier: 4F40BB33-799D-403B-8C2B-16613187D9A1 success: 1 madeChanges: 0 error: (null)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest](3535): <NSCloudKitMirroringDelegate: 0x301060000>: Checking for pending requests.
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: debug: CoreData+CloudKit: -[NSCloudKitMirroringDelegate remoteStoreDidChange:]_block_invoke_2(3194): <NSCloudKitMirroringDelegate: 0x301060000> - Ignoring remote change notification because the exporter has already caught up to this transaction: 3 / 3 - <NSSQLCore: 0x10f504660> (URL: file:///var/mobile/Containers/Data/Application/DB7455E6-34C5-49BA-88C3-B25A4796B833/Library/Application%20Support/ztt2.sqlite)
CoreData: CloudKit: CoreData+CloudKit: -[NSCloudKitMirroringDelegate checkAndExecuteNextRequest]_block_invoke(3551): <NSCloudKitMirroringDelegate: 0x301060000>: No more requests to execute.
Message from debugger: killed